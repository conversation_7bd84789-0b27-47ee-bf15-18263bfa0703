<?php

namespace App\Http\Controllers\Sales;

use App\Http\Controllers\Controller;
use App\Models\UnitTransaction;
use App\Models\Site;
use App\Models\Unit;
use App\Models\Invoice;
use App\Models\JasaKaryawan;
use App\Models\LogAktivitas;
use App\Helpers\NumberToWords;
use App\Models\UnitTransactionPart;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class SalesDashboardController extends Controller
{
    public function index()
    {
        // Get all sites for the filter dropdown
        $sites = Site::all();

        // Get only the specified statuses for the filter dropdown
        $statuses = ['Ready PO', 'Ready WO', 'Perbaikan', 'Pending', 'selesai','MR','On Process'];

        return view('sales.Dashboard', compact('sites', 'statuses'));
    }

    public function getTransactions(Request $request)
    {
        // Set default date range if not provided (1st of current month to today)
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;

        if (empty($dateFrom) || empty($dateTo)) {
            $dateFrom = now()->startOfMonth()->format('Y-m-d');
            $dateTo = now()->format('Y-m-d');
        }

        // Build initial query with date range
        $query = UnitTransaction::with(['unit', 'parts.partInventory.part', 'site', 'invoices'])
            ->whereBetween('created_at', [
                $dateFrom . ' 00:00:00',
                $dateTo . ' 23:59:59'
            ]);

        // Apply sorting if provided
        $sortField = $request->input('sort_field');
        $sortOrder = $request->input('sort_order', 'asc');

        if ($sortField) {
            // Handle special cases for related fields
            if ($sortField === 'site') {
                $query->join('sites', 'unit_transactions.site_id', '=', 'sites.site_id')
                    ->orderBy('sites.site_name', $sortOrder);
            } elseif ($sortField === 'unit') {
                $query->join('units', 'unit_transactions.unit_id', '=', 'units.id')
                    ->orderBy('units.unit_code', $sortOrder);
            } else {
                // Direct field sorting
                $query->orderBy($sortField, $sortOrder);
            }
        } else {
            // Default sorting
            $query->orderBy('created_at', 'desc');
        }

        // Apply site filter if provided
        if ($request->has('site_id') && $request->site_id != '') {
            $query->where('site_id', $request->site_id);
        }

        // Apply status filter if provided
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }

        // Apply unit filter if provided
        if ($request->has('unit_id') && $request->unit_id != '') {
            $query->where('unit_id', $request->unit_id);
        }

        // Apply search if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('wo_number', 'like', "%{$search}%")
                    ->orWhere('po_number', 'like', "%{$search}%")
                    ->orWhere('do_number', 'like', "%{$search}%")
                    ->orWhere('noSPB', 'like', "%{$search}%")
                    ->orWhereHas('unit', function ($unitQuery) use ($search) {
                        $unitQuery->where('unit_code', 'like', "%{$search}%")
                            ->orWhere('unit_type', 'like', "%{$search}%");
                    });
            });
        }

        // Filter to only show transactions without invoices and only include specified statuses
        $query->doesntHave('invoices')
            ->whereIn('status', ['Ready PO', 'Ready WO', 'Pending', 'Perbaikan', 'selesai','On Process','MR']);

        // Paginate the results
        $transactions = $query->paginate(10);

        // Preserve query parameters in pagination links
        $transactions->appends($request->only(['status', 'site_id', 'unit_id', 'search', 'date_from', 'date_to', 'sort_field', 'sort_order']));

        // Make sure we're returning data in the expected format with data as an array
        $response = [
            'data' => array_values($transactions->items()), // Ensure data is a sequential array
            'current_page' => $transactions->currentPage(),
            'last_page' => $transactions->lastPage(),
            'per_page' => $transactions->perPage(),
            'total' => $transactions->total(),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
        ];

        // Set no-cache headers
        return response()->json($response)
            ->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
            ->header('Pragma', 'no-cache')
            ->header('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');
    }

    /**
     * Get invoiced transactions
     */
    public function getInvoicedTransactions(Request $request)
    {
        // Set default date range if not provided (1st of current month to today)
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;
        if (empty($dateFrom) || empty($dateTo)) {
            $dateFrom = now()->startOfMonth()->format('Y-m-d');
            $dateTo = now()->format('Y-m-d');
        }
        $query = Invoice::with(['unitTransactions.unit', 'unitTransactions.parts.partInventory.part', 'penawaran.items.partInventory.part'])
            ->whereHas('unitTransactions')
            ->whereBetween('tanggal_invoice', [
                $dateFrom . ' 00:00:00',
                $dateTo . ' 23:59:59'
            ]);
        $isCompletedInvoicesPage = $request->has('completed') && $request->completed == 'true';
        $isAllInvoicesPage = $request->has('all') && $request->all == 'true';

        if ($isCompletedInvoicesPage) {
            $query->where(function ($q) {
                $q->where('payment_status', 'Lunas')
                    ->orWhere('status', 'Selesai');
            });
        } elseif ($isAllInvoicesPage) {
            // For the all invoices page, show all invoices regardless of status
            // No additional filter needed
        } else {
            // For the main dashboard, exclude invoices with payment status 'Lunas'
            // and exclude invoices with document_path (they should only be shown in the invoice page)
            $query->where('payment_status', '!=', 'Lunas')
                ->whereNull('document_path');
        }

        // Filter by status if provided
        if ($request->has('status') && $request->status != '') {
            $status = $request->status;

            if ($status === 'Lunas') {
                $query->where('payment_status', 'Lunas');
            } elseif ($status === 'Belum Lunas') {
                $query->where('payment_status', 'Belum Lunas');
            } elseif ($status === 'Jatuh Tempo') {
                $query->where(function ($q) {
                    $q->where('payment_status', '!=', 'Lunas')
                        ->where(function ($q2) {
                            $q2->whereDate('due_date', '<', now())
                                ->orWhereNull('due_date')
                                ->whereDate('tanggal_invoice', '<', now()->subDays(30));
                        });
                });
            }
        }

        // Apply sorting if provided
        $sortField = $request->input('sort_field');
        $sortOrder = $request->input('sort_order', 'asc');

        if ($sortField) {
            // Handle special cases for related fields or complex fields
            if ($sortField === 'tanggal_invoice') {
                $query->orderBy('tanggal_invoice', $sortOrder);
            } elseif ($sortField === 'no_invoice') {
                $query->orderBy('no_invoice', $sortOrder);
            } elseif ($sortField === 'customer') {
                $query->orderBy('customer', $sortOrder);
            } elseif ($sortField === 'subtotal' || $sortField === 'ppn' || $sortField === 'total') {
                // For numeric fields, cast to decimal for proper sorting
                $query->orderBy($sortField, $sortOrder);
            } elseif ($sortField === 'payment_status') {
                $query->orderBy('payment_status', $sortOrder);
            } else {
                // Default to created_at for any other field
                $query->orderBy('created_at', 'desc');
            }
        } else {
            // Default sorting by invoice number
            $sortField = 'no_invoice';
            $sortOrder = 'asc';
            $query->orderBy('no_invoice', 'asc');
        }

        // Apply search filter if provided
        if ($request->has('search') && $request->search != '') {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('no_invoice', 'like', "%{$search}%")
                    ->orWhere('customer', 'like', "%{$search}%")
                    ->orWhere('sn', 'like', "%{$search}%")
                    ->orWhereHas('unitTransactions', function ($transactionQuery) use ($search) {
                        $transactionQuery->whereHas('unit', function ($unitQuery) use ($search) {
                            $unitQuery->where('unit_code', 'like', "%{$search}%")
                                ->orWhere('unit_type', 'like', "%{$search}%");
                        });
                    });
            });
        }

        // Get the invoices
        $invoices = $query->paginate(10);

        // Sort the results by invoice number if requested
        if ($sortField === 'no_invoice') {
            $sorted = $invoices->getCollection()->sortBy(function ($invoice) use ($sortOrder) {
                if (!$invoice->no_invoice) {
                    return $sortOrder === 'asc' ? PHP_INT_MAX : PHP_INT_MIN;
                }

                // Extract the numeric part before the first slash
                $parts = explode('/', $invoice->no_invoice);
                $numericPart = (int) $parts[0];

                // Return based on sort order
                return $sortOrder === 'asc' ? $numericPart : -$numericPart;
            });

            // Replace the collection with the sorted one
            $invoices->setCollection($sorted);
        }

        // Add date range to the response
        $invoices->date_from = $dateFrom;
        $invoices->date_to = $dateTo;

        // Preserve query parameters in pagination links
        $invoices->appends($request->only(['search', 'date_from', 'date_to', 'sort_field', 'sort_order', 'status']));

        // Make sure we're returning data in the expected format with data as an array
        $response = [
            'data' => array_values($invoices->items()), // Ensure data is a sequential array
            'current_page' => $invoices->currentPage(),
            'last_page' => $invoices->lastPage(),
            'per_page' => $invoices->perPage(),
            'total' => $invoices->total(),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
        ];

        // Set no-cache headers
        return response()->json($response)
            ->header('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
            ->header('Pragma', 'no-cache')
            ->header('Expires', 'Sat, 01 Jan 2000 00:00:00 GMT');
    }

    public function getTransaction($id)
    {
        $transaction = UnitTransaction::with(['unit', 'parts.partInventory.part', 'site', 'invoices'])
            ->findOrFail($id);

        return response()->json($transaction);
    }

    public function updateStatus(Request $request, $id)
    {
        $transaction = UnitTransaction::findOrFail($id);

        // Validate the request
        $request->validate([
            'status' => 'required|in:Ready PO,Pending,Perbaikan,Selesai',
            'sales_notes' => 'nullable|string',
        ]);

        // Update the transaction status
        $transaction->status = $request->status;

        // Update sales_notes if provided
        if ($request->has('sales_notes')) {
            $transaction->sales_notes = $request->sales_notes;
        }

        // Save the changes
        $transaction->save();

        // Log the status update
        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Update Status Unit Transaction',
            'description' => 'Status transaksi unit ID ' . $transaction->id . ' diubah menjadi ' . $request->status . ' oleh Sales',
            'table' => 'Unit Transaction',
            'ip_address' => $request->ip(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Status berhasil diperbarui',
            'transaction' => $transaction
        ]);
    }

    /**
     * Update sales notes for a transaction
     */
    public function updateSalesNotes(Request $request, $id)
    {
        $transaction = UnitTransaction::findOrFail($id);

        // Validate the request
        $request->validate([
            'sales_notes' => 'required|string',
        ]);

        // Update the sales notes
        $transaction->sales_notes = $request->sales_notes;

        // Save the changes
        $transaction->save();

        // Log the sales notes update
        LogAktivitas::create([
            'site_id' => session('site_id'),
            'name' => session('name'),
            'action' => 'Update Sales Notes Unit Transaction',
            'description' => 'Catatan Sales untuk transaksi unit ID ' . $transaction->id . ' diperbarui oleh Sales',
            'table' => 'Unit Transaction',
            'ip_address' => $request->ip(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Catatan Sales berhasil diperbarui',
            'transaction' => $transaction
        ]);
    }

    /**
     * Get units for a specific site
     */
    public function getUnits(Request $request)
    {
        // Validate the request
        $request->validate([
            'site_id' => 'required|exists:sites,site_id',
        ]);

        // Get units for the specified site
        $units = Unit::where('site_id', $request->site_id)
            ->orderBy('unit_code')
            ->get();

        return response()->json($units);
    }

    /**
     * Preview invoice for a transaction or an invoice
     */
    public function previewInvoice($id)
    {
        try {
            // Check if this is an invoice ID or a transaction ID
            if (strpos($id, 'inv-') === 0) {
                // This is an invoice ID
                $invoiceId = substr($id, 4);

                try {
                    $invoice = Invoice::with('unitTransactions.unit', 'unitTransactions.parts.partInventory.part', 'unitTransactions.site')
                        ->findOrFail($invoiceId);
                } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
                    // Log the error
                    Log::error("Invoice with ID {$invoiceId} not found when trying to preview");

                    // Return a more user-friendly error
                    return response()->view('errors.custom', [
                        'message' => "Terjadi kesalahan: Invoice dengan ID {$invoiceId} tidak ditemukan. Mungkin invoice belum selesai dibuat atau telah dihapus.",
                        'error' => "No query results for model [App\\Models\\Invoice] {$invoiceId}",
                        'back_url' => '/sales/dashboard'
                    ], 404);
                }

                // Get all transactions for this invoice
                $transactions = $invoice->unitTransactions;

                if ($transactions->isEmpty()) {
                    throw new \Exception('No transactions found for this invoice');
                }

                // Use the first transaction for some of the invoice details
                $primaryTransaction = $transactions->first();
            } else {
                // This is a transaction ID
                $transaction = UnitTransaction::with(['unit', 'parts.partInventory.part', 'site', 'invoices'])
                    ->findOrFail($id);

                // Check if invoice exists
                if (!$transaction->isInvoiced()) {
                    throw new \Exception('No invoice found for this transaction');
                }

                // Get the invoice
                $invoice = $transaction->getLatestInvoice();

                // Get all transactions for this invoice
                $transactions = $invoice->unitTransactions;
                $primaryTransaction = $transaction;
            }

            // Calculate total across all transactions
            $subtotal = 0;
            $allParts = [];

            foreach ($transactions as $transaction) {
                // Make sure parts are loaded with their relationships
                $transaction->load('parts.partInventory.part');

                foreach ($transaction->parts as $part) {
                    // Get price from part inventory (site-specific) instead of unit transaction part
                    $price = $part->price ??  $part->partInventory->part->price ;
                    $quantity = $part->quantity;
                    $totalItem = $price * $quantity;
                    $subtotal += $totalItem;

                    // Add to all parts array
                    $allParts[] = [
                        'name' => $part->part_name ?? $part->partInventory->part->part_name ?? 'N/A',
                        'quantity' => $quantity,
                        'unit' => $part->eum ?? $part->partInventory->part->oum ?? 'EA',
                        'price' => $price,
                        'formattedPrice' => number_format($price, 0, ',', '.'),
                        'totalItem' => $totalItem,
                        'formattedTotalItem' => number_format($totalItem, 0, ',', '.'),
                        'transaction_id' => $transaction->id,
                        'unit_code' => $transaction->unit->unit_code ?? 'N/A',
                    ];
                }
            }

            // Calculate total with tax
            $ppnRate = $invoice->ppn ?? 0.11;
            $ppn = $subtotal * $ppnRate;
            $grandTotal = $subtotal + $ppn;

            // Format all monetary values
            $formattedSubtotal = number_format($subtotal, 0, ',', '.');
            $formattedPpn = number_format($ppn, 0, ',', '.');
            $formattedGrandTotal = number_format($grandTotal, 0, ',', '.');

            // Convert total to words
            $terbilang = NumberToWords::convert(round($grandTotal));

            // Format invoice number
            $invoiceNumber = $invoice->no_invoice;

            // Generate PDF
            $pdf = Pdf::loadView('sales.invoice', compact(
                'transactions',
                'primaryTransaction',
                'terbilang',
                'invoiceNumber',
                'allParts',
                'formattedSubtotal',
                'formattedPpn',
                'formattedGrandTotal',
                'invoice',
                'subtotal',  // Pass the calculated subtotal
                'ppnRate',   // Pass the PPN rate
                'ppn'        // Pass the calculated PPN
            ));

            // Set paper size to portrait A4
            $pdf->setPaper('a4', 'portrait');

            // Log the preview action
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Preview Invoice',
                'description' => 'Invoice ' . $invoiceNumber . ' dipreview oleh Sales',
                'table' => 'Invoice',
                'ip_address' => request()->ip(),
            ]);

            // Sanitize invoice number for filename (replace slashes with underscores)
            $safeInvoiceNumber = str_replace(['/', '\\'], '_', $invoiceNumber);

            // Stream the PDF (show in browser)
            return $pdf->stream('invoice_' . $safeInvoiceNumber . '_' . Carbon::now()->format('YmdHis') . '.pdf');
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error previewing invoice: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            // Return an error response
            return response()->view('errors.custom', [
                'message' => 'Terjadi kesalahan saat membuat preview invoice. Silakan coba lagi.',
                'error' => $e->getMessage(),
                'back_url' => '/sales/dashboard'
            ], 500);
        }
    }

    /**
     * Download invoice for a transaction or an invoice
     */
    public function downloadInvoice($id)
    {
        try {
            // Check if this is an invoice ID or a transaction ID
            if (strpos($id, 'inv-') === 0) {
                // This is an invoice ID
                $invoiceId = substr($id, 4);

                try {
                    $invoice = Invoice::with('unitTransactions.unit', 'unitTransactions.parts.partInventory.part', 'unitTransactions.site')
                        ->findOrFail($invoiceId);
                } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
                    // Log the error
                    Log::error("Invoice with ID {$invoiceId} not found when trying to download");

                    // Return a more user-friendly error
                    return response()->view('errors.custom', [
                        'message' => "Terjadi kesalahan: Invoice dengan ID {$invoiceId} tidak ditemukan. Mungkin invoice belum selesai dibuat atau telah dihapus.",
                        'error' => "No query results for model [App\\Models\\Invoice] {$invoiceId}",
                        'back_url' => '/sales/dashboard'
                    ], 404);
                }

                // Get all transactions for this invoice
                $transactions = $invoice->unitTransactions;

                if ($transactions->isEmpty()) {
                    throw new \Exception('No transactions found for this invoice');
                }

                // Use the first transaction for some of the invoice details
                $primaryTransaction = $transactions->first();
            } else {
                // This is a transaction ID
                $transaction = UnitTransaction::with(['unit', 'parts.partInventory.part', 'site', 'invoices'])
                    ->findOrFail($id);

                // Check if invoice exists
                if (!$transaction->isInvoiced()) {
                    throw new \Exception('No invoice found for this transaction');
                }

                // Get the invoice
                $invoice = $transaction->getLatestInvoice();

                // Get all transactions for this invoice
                $transactions = $invoice->unitTransactions;
                $primaryTransaction = $transaction;
            }

            // Calculate total across all transactions
            $subtotal = 0;
            $allParts = [];

            foreach ($transactions as $transaction) {
                // Make sure parts are loaded with their relationships
                $transaction->load('parts.partInventory.part');

                foreach ($transaction->parts as $part) {
                    // Get price from part inventory (site-specific) instead of unit transaction part
                    $price = $part->price ?? $part->partInventory->price;
                    $quantity = $part->quantity;
                    $totalItem = $price * $quantity;
                    $subtotal += $totalItem;

                    // Add to all parts array
                    $allParts[] = [
                        'name' => $part->partInventory->part->part_name ?? 'N/A',
                        'quantity' => $quantity,
                        'unit' => $part->partInventory->part->eum ?? 'Ea',
                        'price' => $price,
                        'formattedPrice' => number_format($price, 0, ',', '.'),
                        'totalItem' => $totalItem,
                        'formattedTotalItem' => number_format($totalItem, 0, ',', '.'),
                        'transaction_id' => $transaction->id,
                        'unit_code' => $transaction->unit->unit_code ?? 'N/A',
                    ];
                }
            }

            // Calculate total with tax
            $ppnRate = $invoice->ppn ?? 0.11;
            $ppn = $subtotal * $ppnRate;
            $grandTotal = $subtotal + $ppn;

            // Format all monetary values
            $formattedSubtotal = number_format($subtotal, 0, ',', '.');
            $formattedPpn = number_format($ppn, 0, ',', '.');
            $formattedGrandTotal = number_format($grandTotal, 0, ',', '.');

            // Convert total to words
            $terbilang = NumberToWords::convert(round($grandTotal));

            // Format invoice number
            $invoiceNumber = $invoice->no_invoice;

            // Generate PDF
            $pdf = Pdf::loadView('sales.invoice', compact(
                'transactions',
                'primaryTransaction',
                'terbilang',
                'invoiceNumber',
                'allParts',
                'formattedSubtotal',
                'formattedPpn',
                'formattedGrandTotal',
                'invoice',
                'subtotal',  // Pass the calculated subtotal
                'ppnRate',   // Pass the PPN rate
                'ppn'        // Pass the calculated PPN
            ));

            // Set paper size to portrait A4
            $pdf->setPaper('a4', 'portrait');

            // Log the download action
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Download Invoice',
                'description' => 'Invoice ' . $invoiceNumber . ' diunduh oleh Sales',
                'table' => 'Invoice',
                'ip_address' => request()->ip(),
            ]);

            // Sanitize invoice number for filename (replace slashes with underscores)
            $safeInvoiceNumber = str_replace(['/', '\\'], '_', $invoiceNumber);

            // Download the PDF
            return $pdf->download('invoice_' . $safeInvoiceNumber . '_' . Carbon::now()->format('YmdHis') . '.pdf');
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error downloading invoice: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            // Return an error response
            return response()->view('errors.custom', [
                'message' => 'Terjadi kesalahan saat mengunduh invoice. Silakan coba lagi.',
                'error' => $e->getMessage(),
                'back_url' => '/sales/dashboard'
            ], 500);
        }
    }



    /**
     * Display the invoices page (only unit transaction invoices)
     */
    public function invoices()
    {
        // Get all sites for the filter dropdown
        $sites = Site::all();

        // Set default date range (current month)
        $dateFrom = now()->startOfMonth()->format('Y-m-d');
        $dateTo = now()->format('Y-m-d');

        return view('sales.invoices', compact('sites', 'dateFrom', 'dateTo'));
    }

    /**
     * Download all attachment files from unit transactions related to an invoice
     */
    public function downloadInvoiceAttachments($id)
    {
        try {
            // Find the invoice
            $invoice = Invoice::with('unitTransactions')->findOrFail($id);

            // Get all unit transactions with attachments
            $unitTransactions = $invoice->unitTransactions->filter(function ($transaction) {
                return !empty($transaction->attachment_path);
            });

            // If there are no attachments, return an error
            if ($unitTransactions->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak ada lampiran yang tersedia untuk invoice ini'
                ], 404);
            }

            // If there's only one attachment, download it directly
            if ($unitTransactions->count() === 1) {
                $transaction = $unitTransactions->first();
                $filePath = public_path('assets/lampiranunits/' . $transaction->attachment_path);

                if (!file_exists($filePath)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'File lampiran tidak ditemukan'
                    ], 404);
                }

                // Log the download action
                LogAktivitas::create([
                    'site_id' => session('site_id'),
                    'name' => session('name'),
                    'action' => 'Download Unit Transaction Attachment',
                    'description' => 'Lampiran Unit Transaction ' . $transaction->id . ' diunduh oleh Sales',
                    'table' => 'UnitTransaction',
                    'ip_address' => request()->ip(),
                ]);

                return response()->download($filePath);
            }

            // If there are multiple attachments, create a zip file
            $zipFileName = 'invoice_' . $id . '_attachments_' . time() . '.zip';
            $zipFilePath = storage_path('app/' . $zipFileName);

            // Create a new zip archive
            $zip = new \ZipArchive();
            if ($zip->open($zipFilePath, \ZipArchive::CREATE) !== true) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal membuat file zip'
                ], 500);
            }

            // Add each attachment to the zip file
            foreach ($unitTransactions as $transaction) {
                $filePath = public_path('assets/lampiranunits/' . $transaction->attachment_path);
                if (file_exists($filePath)) {
                    // Use unit code and transaction ID as the file name in the zip
                    $unitCode = $transaction->unit ? $transaction->unit->unit_code : 'unknown';
                    $fileName = $unitCode . '_' . $transaction->id . '_' . $transaction->attachment_path;
                    $zip->addFile($filePath, $fileName);
                }
            }

            $zip->close();

            // Log the download action
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Download Multiple Unit Transaction Attachments',
                'description' => 'Lampiran Unit Transactions untuk Invoice ' . $id . ' diunduh oleh Sales',
                'table' => 'Invoice',
                'ip_address' => request()->ip(),
            ]);

            // Download the zip file and then delete it
            return response()->download($zipFilePath)->deleteFileAfterSend(true);

        } catch (\Exception $e) {
            // Log the error
            Log::error('Error downloading invoice attachments: ' . $e->getMessage());
            Log::error($e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengunduh lampiran: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the all invoices page (including direct and penawaran invoices)
     */
    public function allInvoices()
    {
        // Get all sites for the filter dropdown
        $sites = Site::all();

        return view('sales.all-invoices', compact('sites'));
    }

    /**
     * Get all invoices including direct and penawaran invoices
     */
    public function getAllInvoices(Request $request)
    {
        $dateFrom = $request->date_from ?: now()->startOfMonth()->format('Y-m-d');
        $dateTo = $request->date_to ?: now()->format('Y-m-d');
        $pagenation = $request->input('per_page') ?? 10;

        // Base query
        $query = Invoice::with([
            'unitTransactions.unit',
            'unitTransactions.parts.partInventory.part',
            'penawaran.items.partInventory.part'
        ])
            ->whereBetween('tanggal_invoice', ["$dateFrom 00:00:00", "$dateTo 23:59:59"]);

        // Site filter
        if ($request->filled('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->status;
            if ($status === 'Lunas' || $status === 'Belum Lunas') {
                $query->where('payment_status', $status);
            } elseif ($status === 'Jatuh Tempo') {
                $query->where(function ($q) {
                    $q->where('payment_status', '!=', 'Lunas')
                        ->where(function ($q2) {
                            $q2->whereDate('due_date', '<', now())
                                ->orWhereNull('due_date')
                                ->whereDate('tanggal_invoice', '<', now()->subDays(30));
                        });
                });
            }
        }

        // Search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('no_invoice', 'like', "%{$search}%");
            });
        }
        if ($request->input('customer')) {
            $query->where('customer', $request->input('customer'));
        }

        // Sorting
        $sortField = $request->input('sort_field', 'tanggal_invoice');
        $sortOrder = $request->input('sort_order', 'desc');

        if ($sortField === 'site') {
            $query->leftJoin('sites', 'invoices.site_id', '=', 'sites.id')
                ->orderBy('sites.name', $sortOrder)
                ->select('invoices.*');
        } else {
            $query->orderBy($sortField, $sortOrder);
        }

        // Clone for total calculation
        $invoicesAll = (clone $query)->get();
        $invoices = $query->paginate($pagenation);

        $totalprice = 0;

        foreach ($invoicesAll as $invoice) {
            $totalprice += $this->calculateInvoiceTotal($invoice);
        }
        $customer = '-';
        $customer = Invoice::whereBetween('tanggal_invoice', ["$dateFrom 00:00:00", "$dateTo 23:59:59"])
            ->select('customer')
            ->distinct()
            ->orderBy('customer')
            ->pluck('customer');

        // Add date range and total invoice to response
        $invoices->date_from = $dateFrom;
        $invoices->date_to = $dateTo;
        $invoices->appends($request->only(['search', 'date_from', 'date_to', 'sort_field', 'sort_order', 'status', 'site_id']));

        return response()->json([
            'data' => array_values($invoices->items()),
            'current_page' => $invoices->currentPage(),
            'last_page' => $invoices->lastPage(),
            'per_page' => $invoices->perPage(),
            'total' => $invoices->total(),
            'totalinvoice' => $totalprice,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'customer' => $customer,
        ]);
    }


    // calculate total
    protected function calculateInvoiceTotal($invoice)
    {
        $subtotal = 0;

        // Jika invoice memiliki total_amount langsung, gunakan itu
        // if (isset($invoice->total_amount)) {
        //     $subtotal = $invoice->total_amount;
        // }
        // Jika invoice dari penawaran
        if ($invoice->penawaran_id && $invoice->penawaran && $invoice->penawaran->items) {
            foreach ($invoice->penawaran->items as $item) {
                if (!$item->partInventory || !$item->quantity)
                    continue;
                $subtotal += $item->price * $item->quantity;
            }
        }
        // Jika invoice manual
        else {
            $manualParts = \App\Models\ManualInvoicePart::with('part')
                ->where('invoice_id', $invoice->id)
                ->get();

            if ($manualParts->isNotEmpty()) {
                foreach ($manualParts as $item) {
                    if (!$item->part)
                        continue;
                    $subtotal += $item->price * $item->quantity;
                }
            }
            // Hitung dari unitTransactions
            else {
                foreach ($invoice->unitTransactions as $transaction) {
                    if (!$transaction->parts)
                        continue;
                    foreach ($transaction->parts as $part) {
                        if (!$part->partInventory || !$part->quantity)
                            continue;
                        $price = $part->price ?? $part->partInventory->price;
                        $subtotal += $price * $part->quantity;
                    }
                }
            }
        }

        $ppn = $invoice->ppn ?? 0.11;
        return $subtotal + ($subtotal * $ppn);
    }

    // update nama dari pembutan invoice
    // TransactionController.php

    public function updatePartValue(Request $request, $id)
    {
        $validated = $request->validate([
            'part_name' => ['nullable', 'string', 'min:2', 'max:200'],
        ]);


        $partItem = UnitTransactionPart::findOrFail($id);
        $partItem->part_name = $validated['part_name'];
        $partItem->save();

        return response()->json(['success' => true, 'part_name' => $partItem->part_name]);
    }

    public function updatePartValueinvoice(Request $request, $id)
    {
        // Check if the ID is a temporary ID (starts with 'temp-')
        if (str_starts_with($id, 'temp-')) {
            return response()->json([
                'success' => false,
                'message' => 'Simpan invoice terlebih dahulu sebelum mengedit part.',
            ], 400);
        }

        $validated = $request->validate([
            'part_name' => ['nullable', 'string', 'min:2', 'max:200'],
        ]);

        try {
            // Update langsung ke table manual_invoice_parts
            $updated = DB::table('manual_invoice_parts')
                ->where('id', $id)
                ->update([
                    'part_name' => $validated['part_name'] ?? null,
                    'updated_at' => now(),
                ]);

            if ($updated) {
                return response()->json([
                    'success' => true,
                    'part_name' => $validated['part_name'],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Part tidak ditemukan atau tidak ada perubahan.',
                ], 404);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }


    public function getpartfromunit($id)
    {
        $transaction = UnitTransaction::with('parts.partInventory.part')->findOrFail($id);
        return response()->json($transaction);
    }

}
