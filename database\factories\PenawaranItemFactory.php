<?php

namespace Database\Factories;

use App\Models\PenawaranItem;
use Illuminate\Database\Eloquent\Factories\Factory;

class PenawaranItemFactory extends Factory
{
    protected $model = PenawaranItem::class;

    public function definition()
    {
        $quantity = $this->faker->numberBetween(1, 10);
        $price = $this->faker->numberBetween(50000, 500000);

        return [
            'penawaran_id' => 1, // Will be overridden in tests
            'part_inventory_id' => 1, // Will be overridden in tests
            'nama_part' => $this->faker->words(3, true),
            'quantity' => $quantity,
            'price' => $price,
            'status' => $this->faker->randomElement(['Ready', 'In Order', 'Not Ready']),
        ];
    }
}
