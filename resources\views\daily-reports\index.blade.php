@extends('sites.content')
@section('title', 'Daily Reports')
@section('resourcesite')
@vite(['resources/js/daily-reports.js'])
@vite(['resources/css/daily-reports.css'])
@endsection

@section('contentsite')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('sites.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Daily Reports</li>
                    </ol>
                </div>
                <h4 class="page-title">Daily Reports</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Data Table Section (Full Width) -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-1 ml-1">
                        <div class="col-md-8">
                            <h4 class="header-title">Daftar Daily Reports</h4>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success" id="export-excel-btn">
                                    <i class="mdi mdi-file-excel me-1"></i> Export Excel
                                </button>
                                <button type="button" class="btn btn-primary" id="add-daily-report-btn" data-bs-toggle="modal" data-bs-target="#add-daily-report-modal">
                                    <i class="mdi mdi-plus-circle me-1"></i> Add New Report
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3 ml-1">
                        <div class="col-md-3">
                            <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="search-input" placeholder="Cari...">
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control pl-2 pt-0 pr-2 pb-0" id="start-date" placeholder="Tanggal Mulai">
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control pl-2 pt-0 pr-2 pb-0" id="end-date" placeholder="Tanggal Akhir">
                        </div>
                    </div>

                    <!-- Loading Skeleton -->
                    <div id="loading-skeleton" class="d-none">
                        <div class="skeleton-loader">
                            <div class="skeleton-row"></div>
                            <div class="skeleton-row"></div>
                            <div class="skeleton-row"></div>
                        </div>
                    </div>

                    <!-- Data Table -->
                    <div class="table-responsive" style="overflow-x: auto; white-space: nowrap;">
                        <table class="table table-striped table-hover" id="daily-reports-table" style="min-width: 1200px; white-space: nowrap;">
                            <thead class="table-dark">
                                <tr>
                                    <th class="sortable" data-sort="date" style="cursor: pointer; white-space: nowrap; min-width: 100px;">
                                        Date <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="unit" style="cursor: pointer; white-space: nowrap; min-width: 120px;">
                                        Unit <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="hm" style="cursor: pointer; white-space: nowrap; min-width: 80px;">
                                        HM <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="problem" style="cursor: pointer; white-space: nowrap; min-width: 120px;">
                                        Problem <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="problem_component" style="cursor: pointer; white-space: nowrap; min-width: 150px;">
                                        Problem Component <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="problem_description" style="cursor: pointer; white-space: nowrap; min-width: 200px;">
                                        Problem Description <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th style="white-space: nowrap; min-width: 200px;">Job Description</th>
                                    <th class="sortable" data-sort="start" style="cursor: pointer; white-space: nowrap; min-width: 80px;">
                                        Start <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="finish" style="cursor: pointer; white-space: nowrap; min-width: 80px;">
                                        Finish <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="shift" style="cursor: pointer; white-space: nowrap; min-width: 80px;">
                                        Shift <i class="mdi mdi-sort"></i>
                                    </th>
                                    <th style="white-space: nowrap; min-width: 100px;">Down Time</th>
                                    <th style="white-space: nowrap; min-width: 150px;">Man Power</th>
                                    <th style="white-space: nowrap; min-width: 100px;">TAR File</th>
                                    <th style="white-space: nowrap; min-width: 100px;">Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="daily-reports-table-body">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div id="pagination-container" class="d-flex justify-content-center mt-3">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>
            </div>
        </div>


    </div>
</div>



<!-- View Daily Report Modal -->
<div class="modal fade" id="view-daily-report-modal" tabindex="-1" aria-labelledby="view-daily-report-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="view-daily-report-modal-label">Detail Daily Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="daily-report-details">
                <!-- Loading spinner -->
                <div id="detail-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">.</span>
                    </div>
                    <p class="mt-2 text-muted">Memuat detail laporan...</p>
                </div>
                <!-- Details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<!-- Add New Daily Report Modal (Full Screen) -->
<div class="modal fade" id="add-daily-report-modal" tabindex="-1" aria-labelledby="add-daily-report-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title h4 text-uppercase" id="add-daily-report-modal-label">Add New Daily Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading spinner for modal -->
                <div id="modal-loading" class="text-center py-4 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">.</span>
                    </div>
                    <p class="mt-2 text-muted">Menyimpan data...</p>
                </div>

                <!-- Table-like Form Layout -->
                <div class="p-4">
                    <form id="modal-daily-report-form" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col p-4">
                                <input type="hidden" id="modal-daily-report-id" name="id">
                                <!-- Unit Auto-Search -->
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">Unit <span class="text-danger">*</span></label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_unit_search" placeholder="Cari unit berdasarkan kode atau tipe..." autocomplete="off">
                                        <input type="hidden" id="modal_unit_id" name="unit_id" required>
                                        <div id="modal_unit_dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                            <!-- Search results will appear here -->
                                        </div>
                                    </div>
                                </div>

                                <!-- HM -->
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">HM <span class="text-danger">*</span></label>
                                    <div class="col-md-8">
                                        <input type="number" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_hm" name="hm" step="0.01" min="0" placeholder="Hour Meter" required>
                                    </div>
                                </div>
                                <!-- Problem Fields -->
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">Problem</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_problem" name="problem">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">Problem Component</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_problem_component" name="problem_component">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">Problem Description</label>
                                    <div class="col-md-8">
                                        <textarea class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_problem_description" name="problem_description" rows="3"></textarea>
                                    </div>
                                </div>
                                <!-- Date and Time -->
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">Tanggal <span class="text-danger">*</span></label>
                                    <div class="col-md-8">
                                        <input type="date" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_date_in" name="date_in" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="modal_hour_in" class="form-label">Jam Masuk <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_hour_in" name="hour_in" placeholder="07:00 atau SHIFT PAGI" required>
                                            <small class="text-muted">Format: 07:00 atau teks seperti 'SHIFT PAGI'</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="modal_hour_out" class="form-label">Jam Keluar <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_hour_out" name="hour_out" placeholder="15:00 atau SHIFT PAGI" required>
                                            <small class="text-muted">Format: 15:00 atau teks seperti 'SHIFT PAGI'</small>
                                        </div>
                                    </div>
                                </div>
                                <!-- Shift -->
                                <div class="mb-3">
                                    <label for="modal_shift" class="form-label">Shift<span class="text-danger">*</span></label>
                                    <select class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_shift" name="shift" required>
                                        <option value="">Pilih Shift</option>
                                        <option selected value="DAY">DAY</option>
                                        <option value="NIGHT">NIGHT</option>
                                    </select>
                                </div>
                                <!-- Plan Fix -->
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">Plan Fix to TAR</label>
                                    <div class="col-md-8">
                                        <textarea class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_plan_fix" name="plan_fix" rows="2"></textarea>
                                    </div>
                                </div>
                                <!-- Plan Rekomen -->
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">Plan Rekomen to TAR</label>
                                    <div class="col-md-8">
                                        <textarea class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_plan_rekomen" name="plan_rekomen" rows="2"></textarea>
                                    </div>
                                </div>
                                <!-- Lifetime Component -->
                                <div class="form-group row">
                                    <label class="col-md-4 col-form-label">Lifetime Component to TAR</label>
                                    <div class="col-md-8">
                                        <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_lifetime_component" name="lifetime_component">
                                    </div>
                                </div>
                            </div>
                            <div class="col p-4">
                                <div class="form-group row">
                                    <label class="col-md-2 col-form-label">Pekerjaan <span class="text-danger">*</span></label>
                                    <!-- Inline Job Creation Form -->
                                    <div class="col-md-10">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_job_description_input" placeholder="Deskripsi pekerjaan...">
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="modal_job_highlight_input">
                                                    <label class="form-check-label" for="modal_job_highlight_input">
                                                        Mark
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-primary btn-sm" id="modal_add_job_btn">
                                                    <i class="mdi mdi-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Jobs List -->
                                <div id="modal_jobs_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                    <small class="text-muted">Belum ada pekerjaan ditambahkan</small>
                                </div>
                                <small class="text-muted">Minimal satu pekerjaan harus ditambahkan</small>
                                <!-- Technicians Management -->
                                <div class="form-group row">
                                    <label class="col-md-2 col-form-label">Teknisi <span class="text-danger">*</span></label>
                                    <div class="col-md-10">
                                        <div class="row">
                                            <div class="col-md-10">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_technician_name_input" placeholder="Nama teknisi...">
                                            </div>
                                            <div class="col-md-2">
                                                <button type="button" class="btn btn-primary btn-sm" id="modal_add_technician_btn">
                                                    <i class="mdi mdi-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Technicians List -->
                                <div id="modal_technicians_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                    <small class="text-muted">Belum ada teknisi ditambahkan</small>
                                </div>
                                <!-- Part Problems Management -->
                                <label class="col-md-2 col-form-label">Part Problems</label>
                                <div class="form-group row ml-2">
                                    <div class="col">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_part_code_input" placeholder="Kode Part...">
                                            </div>
                                            <div class="col-md-4">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_part_name_input" placeholder="Nama Part...">
                                            </div>
                                            <div class="col-md-1">
                                                <input type="number" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_part_quantity_input" placeholder="Qty" step="0.01" min="0">
                                            </div>
                                            <div class="col-md-1">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_part_eum_input" placeholder="Satuan...">
                                            </div>
                                            <div class="col-md-2">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="modal_part_remarks_input" placeholder="Keterangan...">
                                            </div>
                                            <div class="col">
                                                <button type="button" class="btn btn-primary btn-sm py-2 px-2" id="modal_add_part_problem_btn">
                                                    <i class="mdi mdi-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Part Problems List -->
                                <div id="modal_part_problems_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                    <small class="text-muted">Belum ada part problem ditambahkan</small>
                                </div>
                                <small class="text-muted">Part problems akan ditampilkan di PDF</small>
                                <label class="form-label d-block pt-2">Upload Gambar</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="text-center border rounded p-3 mb-2" style="background-color: #f8f9fa;">
                                            <h6 class="mt-2 mb-1">Gambar Sebelum</h6>
                                            <label for="modal_before_images" class="btn btn-primary btn-sm">Choose File</label>
                                            <input type="file" class="form-control-file" id="modal_before_images" name="before_images[]" multiple accept="image/*" style="display: none;">
                                            <div id="modal-before-images-preview" class="mt-2"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center border rounded p-3 mb-2" style="background-color: #f8f9fa;">
                                            <h6 class="mt-2 mb-1">Gambar Sesudah</h6>
                                            <label for="modal_after_images" class="btn btn-primary btn-sm">Choose File</label>
                                            <input type="file" class="form-control-file" id="modal_after_images" name="after_images[]" multiple accept="image/*" style="display: none;">
                                            <div id="modal-after-images-preview" class="mt-2"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center border rounded p-3 mb-2" style="background-color: #f8f9fa;">
                                            <h6 class="mt-2 mb-1">Gambar Unit</h6>
                                            <label for="modal_unit_images" class="btn btn-primary btn-sm">Choose File</label>
                                            <input type="file" class="form-control-file" id="modal_unit_images" name="unit_images[]" multiple accept="image/*" style="display: none;">
                                            <div id="modal-unit-images-preview" class="mt-2"></div>
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted">Format: JPEG, PNG, JPG,</small>
                            </div>
                        </div>
                        <!-- Backlog Toggle Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <button type="button" class="btn btn-outline-primary" id="toggle-backlog-btn" onclick="toggleBacklogSection()">
                                            <i class="mdi mdi-plus-circle me-1"></i> Tambah Backlog
                                        </button>
                                    </div>
                                    <div class="card-body" id="backlog-section" style="display: none;">
                                        <h5 class="card-title">Buat Backlog Baru</h5>
                                        <input type="hidden" id="create_backlog" name="create_backlog" value="0">

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="backlog_unit_code" class="form-label">Unit Code <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="backlog_unit_code" name="backlog[unit_code]" placeholder="Kode unit...">
                                                    <small class="text-muted">Akan diisi otomatis jika ada unit pada daily report</small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="backlog_hm_found" class="form-label">HM Found</label>
                                                    <input type="number" class="form-control" id="backlog_hm_found" name="backlog[hm_found]" step="0.01" min="0" placeholder="Hour meter ditemukan...">
                                                    <small class="text-muted">Akan diisi otomatis jika ada HM pada daily report</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="backlog_problem_description" class="form-label">Problem Description <span class="text-danger">*</span></label>
                                                    <textarea class="form-control" id="backlog_problem_description" name="backlog[problem_description]" rows="3" placeholder="Deskripsi masalah..."></textarea>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="backlog_job" class="form-label">Backlog Job <span class="text-danger">*</span></label>
                                                    <textarea class="form-control" id="backlog_job" name="backlog[backlog_job]" rows="3" placeholder="Pekerjaan backlog..."></textarea>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="backlog_plan_hm" class="form-label">Plan HM</label>
                                                    <input type="number" class="form-control" id="backlog_plan_hm" name="backlog[plan_hm]" step="0.01" min="0" placeholder="Rencana HM...">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="backlog_status" class="form-label">Status <span class="text-danger">*</span></label>
                                                    <select class="form-control" id="backlog_status" name="backlog[status]">
                                                        <option value="">Pilih Status</option>
                                                        <option value="OPEN" selected>OPEN</option>
                                                        <option value="CLOSED">CLOSED</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="backlog_plan_pull_date" class="form-label">Plan Pull Date</label>
                                                    <input type="datetime-local" class="form-control" id="backlog_plan_pull_date" name="backlog[plan_pull_date]">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="backlog_notes" class="form-label">Notes</label>
                                            <textarea class="form-control" id="backlog_notes" name="backlog[notes]" rows="2" placeholder="Catatan tambahan..."></textarea>
                                        </div>
                                        <!-- Parts Management for Backlog -->
                                        <div class="mb-3">
                                            <label class="form-label">Parts <span class="text-danger">*</span></label>
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <div class="position-relative">
                                                                <input type="text" class="form-control" id="backlog_part_search" placeholder="Cari part..." autocomplete="off">
                                                                <div id="backlog_part_dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto; position: absolute; top: 100%; left: 0; z-index: 1000;">
                                                                    <!-- Search results will appear here -->
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <input type="number" class="form-control" id="backlog_part_quantity" placeholder="Quantity" min="1" value="1">
                                                        </div>
                                                        <div class="col-md-3">
                                                            <button type="button" class="btn btn-primary" id="add-backlog-part-btn">
                                                                <i class="mdi mdi-plus"></i> Tambah
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="table-responsive">
                                                        <table class="table table-sm table-bordered" id="backlog-parts-table">
                                                            <thead class="table-light">
                                                                <tr>
                                                                    <th>Part Code</th>
                                                                    <th>Part Name</th>
                                                                    <th>Quantity</th>
                                                                    <th>Action</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="backlog-parts-table-body">
                                                                <!-- Parts will be added here -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <div id="backlog-parts-empty-message" class="text-center text-muted py-3">
                                                        <i class="mdi mdi-package-variant"></i><br>
                                                        Belum ada part yang ditambahkan.<br>
                                                        <small>Setiap backlog harus memiliki minimal 1 part.</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" id="modal-save-btn">
                        <i class="mdi mdi-content-save"></i> Simpan
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Daily Report Modal (Full Screen) -->
<div class="modal fade" id="edit-daily-report-modal" tabindex="-1" aria-labelledby="edit-daily-report-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="edit-daily-report-modal-label">Edit Daily Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading spinner for edit modal -->
                <div id="edit-modal-loading" class="text-center py-4 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">.</span>
                    </div>
                    <p class="mt-2 text-muted">Menyimpan data...</p>
                </div>

                <form id="edit-modal-daily-report-form" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" id="edit-modal-daily-report-id" name="id">

                    <div class="row pl-4 pr-4">
                        <div class="col-md-6 p-4">
                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Unit <span class="text-danger">*</span></label>
                                <div class="col-md-9">
                                    <div class="position-relative">
                                        <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_unit_search" placeholder="Cari unit berdasarkan kode atau tipe..." autocomplete="off">
                                        <input type="hidden" id="edit_modal_unit_id" name="unit_id" required>
                                    </div>
                                    <div id="edit_modal_unit_dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                        <!-- Search results will appear here -->
                                    </div>
                                </div>
                            </div>

                            <!-- HM -->
                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">HM</label>
                                <div class="col-md-9">
                                    <input type="number" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_hm" name="hm" step="0.01" min="0" placeholder="Hour Meter">
                                </div>
                            </div>

                            <!-- Problem Fields -->
                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Problem</label>
                                <div class="col-md-9">
                                    <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_problem" name="problem">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Problem Component</label>
                                <div class="col-md-9">
                                    <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_problem_component" name="problem_component">
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Problem Description</label>
                                <div class="col-md-9">
                                    <textarea class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_problem_description" name="problem_description" rows="3"></textarea>
                                </div>
                            </div>

                            <!-- Date and Time -->
                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Tanggal <span class="text-danger">*</span></label>
                                <div class="col-md-9">
                                    <input type="date" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_date_in" name="date_in" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_modal_hour_in" class="form-label">Jam Masuk <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_hour_in" name="hour_in" placeholder="07:00 atau SHIFT PAGI" required>
                                        <small class="text-muted">Format: 07:00 atau teks seperti 'SHIFT PAGI'</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="edit_modal_hour_out" class="form-label">Jam Keluar <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_hour_out" name="hour_out" placeholder="15:00 atau SHIFT PAGI" required>
                                        <small class="text-muted">Format: 15:00 atau teks seperti 'SHIFT PAGI'</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Shift -->
                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Shift <span class="text-danger">*</span></label>
                                <div class="col-md-9">
                                    <select class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_shift" name="shift" required>
                                        <option value="">Pilih Shift</option>
                                        <option selected value="DAY">DAY</option>
                                        <option value="NIGHT">NIGHT</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Plan Fix -->
                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Plan Fix To TAR</label>
                                <div class="col-md-9">
                                    <textarea class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_plan_fix" name="plan_fix" rows="2"></textarea>
                                </div>
                            </div>

                            <!-- Plan Rekomen -->
                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Plan Rekomen To TAR</label>
                                <div class="col-md-9">
                                    <textarea class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_plan_rekomen" name="plan_rekomen" rows="2"></textarea>
                                </div>
                            </div>

                            <!-- Lifetime Component -->
                            <div class="form-group row">
                                <label class="col-md-3 col-form-label">Lifetime Component To TAR</label>
                                <div class="col-md-9">
                                    <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_lifetime_component" name="lifetime_component">
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Pekerjaan <span class="text-danger">*</span></label>
                                <div class="border rounded p-3 mb-3 bg-light">
                                    <div class="row">
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-primary btn-sm" id="edit_modal_add_job_btn">
                                                <i class="mdi mdi-plus"></i>
                                            </button>
                                        </div>
                                        <div class="col-md-1">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="edit_modal_job_highlight_input">
                                                <label class="form-check-label" for="edit_modal_job_highlight_input">
                                                    Mark
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-10">
                                            <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_job_description_input" placeholder="Deskripsi pekerjaan...">
                                        </div>
                                    </div>
                                </div>

                                <!-- Jobs List -->
                                <div id="edit_modal_jobs_list" style="background-color: #f8f9fa;" class="pl-3">
                                    <small class="text-muted">Belum ada pekerjaan ditambahkan</small>
                                </div>
                                <small class="text-muted">Minimal satu pekerjaan harus ditambahkan</small>
                            </div>

                            <!-- Technicians Management -->
                            <div class="mb-3">
                                <label class="form-label">Teknisi <span class="text-danger">*</span></label>
                                <div class="border rounded p-3 mb-3 bg-light">
                                    <div class="row">
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-primary btn-sm w-100" id="edit_modal_add_technician_btn">
                                                <i class="mdi mdi-plus"></i> 
                                            </button>
                                        </div>
                                        <div class="col-md-11">
                                            <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_technician_name_input" placeholder="Nama teknisi...">
                                        </div>
                                    </div>
                                </div>

                                <!-- Technicians List -->
                                <div id="edit_modal_technicians_list" class="border rounded p-2" style="min-height: 60px; background-color: #f8f9fa;">
                                    <small class="text-muted">Belum ada teknisi ditambahkan</small>
                                </div>
                                <small class="text-muted">Minimal satu teknisi harus ditambahkan</small>

                                <!-- Part Problems Management -->
                                <div class="mb-3">
                                    <label class="form-label">Part Problems</label>
                                    <div class="border rounded p-3 mb-3 bg-light">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_part_code_input" placeholder="Kode Part...">
                                            </div>
                                            <div class="col-md-3">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_part_name_input" placeholder="Nama Part...">
                                            </div>
                                            <div class="col-md-2">
                                                <input type="number" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_part_quantity_input" placeholder="Qty..." step="0.01" min="0">
                                            </div>
                                            <div class="col-md-1">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_part_eum_input" placeholder="Satuan...">
                                            </div>
                                            <div class="col-md-3">
                                                <input type="text" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_part_remarks_input" placeholder="Keterangan...">
                                            </div>
                                            <div class="col-md-1">
                                                <button type="button" class="btn btn-primary btn-sm" id="edit_modal_add_part_problem_btn">
                                                    <i class="mdi mdi-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Part Problems List -->
                                    <div id="edit_modal_part_problems_list" style="background-color: #f8f9fa;" class="pl-3">
                                        <small class="text-muted">Belum ada part problem ditambahkan</small>
                                    </div>
                                    <small class="text-muted">Part problems akan ditampilkan di PDF</small>
                                </div>
                            </div>

                            <!-- Existing Images -->
                            <div class="mb-3">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>Gambar Sebelum</h6>
                                        <div id="edit-existing-before-images" class="mb-2"></div>
                                        <div class="mb-3" style="position: relative;">
                                        <label for="edit_modal_before_images" class="form-label">Tambah Gambar Sebelum</label><br>
                                        <label for="edit_modal_before_images" style="display: inline-block; padding: 6px 12px; cursor: pointer; background-color: #0d6efd; color: white; font-size: 14px; border-radius: 4px; margin-top: 5px;">Pilih File</label>
                                        <input type="file" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_before_images" name="before_images[]" multiple accept="image/*"
                                            style="opacity: 0; position: absolute; width: 1px; height: 1px; overflow: hidden; z-index: -1;">
                                        <div id="edit-modal-before-images-preview" class="mt-2"></div>
                                    </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Gambar Sesudah</h6>
                                        <div id="edit-existing-after-images" class="mb-2"></div>
                                         <div class="mb-3" style="position: relative;">
                                        <label for="edit_modal_after_images" class="form-label">Tambah Gambar Sesudah</label><br>
                                        <label for="edit_modal_after_images" style="display: inline-block; padding: 6px 12px; cursor: pointer; background-color: #0d6efd; color: white; font-size: 14px; border-radius: 4px; margin-top: 5px;">Pilih File</label>
                                        <input type="file" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_after_images" name="after_images[]" multiple accept="image/*"
                                            style="opacity: 0; position: absolute; width: 1px; height: 1px; overflow: hidden; z-index: -1;">
                                        <div id="edit-modal-after-images-preview" class="mt-2"></div>
                                    </div>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Gambar Unit</h6>
                                        <div id="edit-existing-unit-images" class="mb-2"></div>
                                        <div class="mb-3" style="position: relative;">
                                        <label for="edit_modal_unit_images" class="form-label">Tambah Gambar Unit</label><br>
                                        <label for="edit_modal_unit_images" style="display: inline-block; padding: 6px 12px; cursor: pointer; background-color: #0d6efd; color: white; font-size: 14px; border-radius: 4px; margin-top: 5px;">Pilih File</label>
                                        <input type="file" class="form-control pl-2 pt-0 pr-2 pb-0" id="edit_modal_unit_images" name="unit_images[]" multiple accept="image/*"
                                            style="opacity: 0; position: absolute; width: 1px; height: 1px; overflow: hidden; z-index: -1;">
                                        <div id="edit-modal-unit-images-preview" class="mt-2"></div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="edit-modal-save-btn">
                    <i class="mdi mdi-content-save"></i> Update
                </button>
            </div>
        </div>
    </div>
</div>
@endsection