<?php

namespace Database\Factories;

use App\Models\Penawaran;
use Illuminate\Database\Eloquent\Factories\Factory;

class PenawaranFactory extends Factory
{
    protected $model = Penawaran::class;

    public function definition()
    {
        return [
            'nomor' => 'PNW-' . $this->faker->unique()->numerify('####'),
            'tanggal_penawaran' => $this->faker->date(),
            'perihal' => $this->faker->sentence(4),
            'customer' => $this->faker->company(),
            'customer_code' => $this->faker->regexify('[A-Z]{4}[0-9]{3}'),
            'attn' => $this->faker->name(),
            'lokasi' => $this->faker->city(),
            'showcode' => $this->faker->randomElement([0, 1]),
            'notes' => $this->faker->optional()->paragraph(),
            'diskon' => $this->faker->numberBetween(0, 20),
            'status' => $this->faker->randomElement(['Draft', 'Dikirim ke customer', 'Disetujui customer', 'Ditolak customer']),
        ];
    }
}
