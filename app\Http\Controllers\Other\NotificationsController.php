<?php

namespace App\Http\Controllers\Other;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\Requisition;
use App\Models\LogAktivitas;
use Illuminate\Http\Request;

class NotificationsController extends Controller
{
    public function destroy(Request $request, $id)
    {
        $notification = Notification::find($id);
        if ($notification) {
            // Log the notification deletion before deleting
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Menghapus Notification',
                'description' => "User " . session('name') . " menghapus Notification: " . $notification->message,
                'table' => "Notifications",
                'ip_address' => $request->ip(),
            ]);

            $notification->delete();
            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false, 'message' => 'Notifikasi tidak ditemukan']);
    }
    public function index()
    {
        $siteId = session('site_id');
        $notifications = Notification::where('site_id', $siteId)
            ->where('is_read', false)
            ->latest()
            ->get();
        $notifications = $notifications->map(function ($notification) {
            return [
                'id' => $notification->id,
                'title' => $notification->title,
                'message' => $notification->message,
                'type' => $notification->type,
                'is_read' => $notification->is_read,
                'routes' => ($notification->from_site) ? route($notification->routes, ['id_site' => $notification->from_site]) : route($notification->routes)
            ];
        });
        return response()->json($notifications);
    }

    public function markAsRead()
    {
        $siteId = session('current_site_id');
        Notification::where('site_id', $siteId)->update(['is_read' => true]);
        return response()->json(['success' => true]);
    }


    public function getcount()
    {
        try {
            $pengajuans = Requisition::whereIn('status', ['diajukan','pending'])
                ->get();
            $jumlah = count($pengajuans);
            return  response()->json(['jumlah' => $jumlah]);
        } catch (\Throwable $th) {
            return  response()->json(['jumlah' => 0]);
        }
    }
}
