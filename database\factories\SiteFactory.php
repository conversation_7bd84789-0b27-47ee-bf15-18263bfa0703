<?php

namespace Database\Factories;

use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class SiteFactory extends Factory
{
    protected $model = Site::class;

    public function definition()
    {
        return [
            'site_id' => 'SID' . Str::random(10),
            'site_name' => $this->faker->company(),
            'address' => $this->faker->address(),
        ];
    }
}
