<?php

namespace App\Http\Controllers\Superadmin;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\JasaKaryawan;
use App\Models\ManualInvoicePart;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\Requisition;
use App\Models\Penawaran;
use App\Models\Site;
use App\Models\UnitTransaction;
use App\Models\UnitTransactionPart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Schema;
use Barryvdh\DomPDF\Facade\Pdf;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class SuperadminController extends Controller
{
    /**
     * Display the superadmin dashboard
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function dashboard(Request $request)
    {
        // Get date range parameters
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');

        // Get filter parameters
        $divisionFilter = $request->input('division');
        $siteFilter = $request->input('site');

        // Initialize date variables
        $startDate = null;
        $endDate = null;
        $date = null;
        $selectedMonth = null;
        $monthName = null;
        $dateRangeText = null;

        // Handle date range parameters if provided
        if ($startDateParam && $endDateParam) {
            // Parse date range parameters
            $startDate = Carbon::parse($startDateParam)->startOfDay();
            $endDate = Carbon::parse($endDateParam)->endOfDay();

            // Format for display
            $startDateFormatted = $startDate->locale('id')->translatedFormat('j F Y');
            $endDateFormatted = $endDate->locale('id')->translatedFormat('j F Y');
            $dateRangeText = "$startDateFormatted - $endDateFormatted";

            // Use the middle date for month-based functions
            $date = $startDate->copy()->addDays($endDate->diffInDays($startDate) / 2);

            // Format dates for database queries
            $startDateDb = $startDate->format('Y-m-d H:i:s');
            $endDateDb = $endDate->format('Y-m-d H:i:s');
        } else {
            // Fallback to current month if no date range provided
            $now = Carbon::now();
            $selectedMonth = $request->input('month', $now->format('Y-m'));
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $monthName = $date->locale('id')->translatedFormat('F Y');

            // Set date range to the entire month
            $startDate = $date->copy()->startOfMonth();
            $endDate = $date->copy()->endOfMonth();

            // Format dates for database queries
            $startDateDb = $startDate->format('Y-m-d H:i:s');
            $endDateDb = $endDate->format('Y-m-d H:i:s');
        }

        // Get previous period for comparison (same length as selected period)
        $periodLength = $endDate->diffInDays($startDate) + 1;
        $prevStartDate = $startDate->copy()->subDays($periodLength);
        $prevEndDate = $prevStartDate->copy()->addDays($periodLength - 1);
        $prevDate = $prevStartDate->copy()->addDays($periodLength / 2);

        // Get site income data for the selected period with filters
        $siteIncomeData = $this->getSiteIncomeData($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Get financial data with filters
        $costData = $this->getCostData($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);
        $profitMarginData = $this->getProfitMarginData($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);
        $accountsReceivableData = $this->getPIUTANGInvoices($startDateDb, $endDateDb, $siteFilter, $divisionFilter);
        $totalpenjualan = $this->gettotalinvoice($startDateDb, $endDateDb, $siteFilter, $divisionFilter);
        $accountsPayableData = $this->getTotalInvoiceALL($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Get site-specific unit counts with filters
        $siteUnitCounts = $this->getSiteUnitCounts($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Get site details for each site
        $siteDetails = [];
        foreach ($siteIncomeData as $incomeData) {
            $siteId = $incomeData['site_id'];

            // Skip if site filter is applied and doesn't match
            if ($siteFilter && $siteFilter !== $siteId) {
                continue;
            }

            // Get repair/pending invoices
            $repairPendingInvoices = $this->getRepairPendingInvoices($siteId, $startDateDb, $endDateDb, $divisionFilter);

            // Get parts not ready (stock <= min_stock)
            $partsNotReady = $this->getPartsNotReady($siteId, '', $divisionFilter);

            // Get pending requests to HO
            $pendingRequests = $this->getPendingRequests($siteId, $startDateDb, $endDateDb, $divisionFilter);

            // Get best-selling parts by type
            $bestPartSales = $this->getBestPartSalesByType($siteId, $startDateDb, $endDateDb, $divisionFilter);

            $siteDetails[$siteId] = [
                'repair_pending_invoices' => $repairPendingInvoices,
                'part_not_ready' => $partsNotReady,
                'pending_requests' => $pendingRequests,
                'best_part_sales' => $bestPartSales
            ];
        }

        // Get previous period data for comparison with filters
        $prevStartDateDb = $prevStartDate->format('Y-m-d H:i:s');
        $prevEndDateDb = $prevEndDate->format('Y-m-d H:i:s');
        $prevSiteIncomeData = $this->getSiteIncomeData($prevDate, $divisionFilter, $siteFilter, $prevStartDateDb, $prevEndDateDb);

        // Combine site income data with unit counts and details
        $sitesData = [];
        foreach ($siteIncomeData as $incomeData) {
            $siteId = $incomeData['site_id'];

            // Skip if site filter is applied and doesn't match
            if ($siteFilter && $siteFilter !== $siteId) {
                continue;
            }

            // Find previous period data for this site
            $prevSiteData = null;
            foreach ($prevSiteIncomeData as $prevData) {
                if ($prevData['site_id'] === $siteId) {
                    $prevSiteData = $prevData;
                    break;
                }
            }

            // Calculate trend (percentage change from previous period)
            $trend = 0;
            $trendDirection = 'stable'; // 'up', 'down', or 'stable'

            if ($prevSiteData) {
                $prevPercentage = $prevSiteData['percentage_of_target'];
                $currentPercentage = $incomeData['percentage_of_target'];

                $trend = $currentPercentage - $prevPercentage;

                if ($trend > 0) {
                    $trendDirection = 'up';
                } elseif ($trend < 0) {
                    $trendDirection = 'down';
                }
            }

            // Get POs and Invoices data for this site with filters
            $posAndInvoicesData = $this->getSitePOsAndInvoicesData($siteId, $date, $divisionFilter, $startDateDb, $endDateDb);

            $sitesData[$siteId] = array_merge(
                $incomeData,
                $siteUnitCounts[$siteId] ?? [],
                [
                    'details' => $siteDetails[$siteId] ?? [],
                    'trend' => round($trend, 1),
                    'trend_direction' => $trendDirection,
                    'total_pos_amount' => $posAndInvoicesData['total_pos_amount'],
                    'totalpo_amount' => $posAndInvoicesData['ready_invoices_amount'],
                    'total_pos_count' => $posAndInvoicesData['total_pos_count'],
                    'ready_invoices_count' => $posAndInvoicesData['ready_invoices_count'],
                    'invoiced_amount' => $posAndInvoicesData['invoiced_amount'],
                    'invoiced_count' => $posAndInvoicesData['invoiced_count'],
                    'pending_po_amount' => $posAndInvoicesData['pending_po_amount'],
                    'pending_po_count' => $posAndInvoicesData['pending_po_count'],
                    'invoice_percentage' => $posAndInvoicesData['percentage']
                ]
            );
        }

        // Get additional dashboard information with filters
        $totalInvoiceAmount = $this->getTotalInvoiceALL($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);
        // Calculate profit margin percentage: (Laba Bersih ÷ Piutang) × 100%
        $profitMarginPercentage = $accountsReceivableData > 0 ? ($totalInvoiceAmount / $accountsReceivableData) * 100 : 0;

        // Get previous period's total invoice amount for comparison with filters
        $prevTotalInvoiceAmount = $this->getTotalInvoiceALL($prevDate, $divisionFilter, $siteFilter, $prevStartDateDb, $prevEndDateDb);

        // Calculate comparison with previous period
        $invoiceDifference = $totalInvoiceAmount - $prevTotalInvoiceAmount;
        $invoiceDifferencePercent = $prevTotalInvoiceAmount > 0 ? round(($invoiceDifference / $prevTotalInvoiceAmount) * 100, 1) : 0;

        // Get units data with filters
        $unitsReadyForWO = $this->getUnitsReadyForWO($divisionFilter, $siteFilter);
        $unitsReadyForPO = $this->getUnitsReadyForPO($divisionFilter, $siteFilter);
        $unitsInProgress = $this->getUnitsInProgress($divisionFilter, $siteFilter);

        // Get units with "Belum PO" and "Proses PO" status for the selected period with filters
        $unitsBelumPO = $this->getUnitsBelumPO($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);
        $unitsProsesPO = $this->getUnitsProsesPO($date, $divisionFilter, $siteFilter, $startDateDb, $endDateDb);

        // Best parts data moved to parts page

        // Get monthly invoice data for the entire year with filters
        $currentYear = $date->year;
        $monthlyInvoiceData = $this->getMonthlyInvoiceData($currentYear, $divisionFilter, $siteFilter);
        // Get totalMonthly Reportamount for the selected period with filters
        $totalJasaKaryawanAmount = $this->getJasaKaryawanAmount($date, null, $siteFilter, $startDateDb, $endDateDb);
        $doneJasaKaryawanAmount = $this->getJasaKaryawanAmount($date, 'done', $siteFilter, $startDateDb, $endDateDb);

        // Get allMonthly Reportdata for the selected period with filters
        $jasaKaryawanData = $this->getAllJasaKaryawanData($date, $siteFilter, $startDateDb, $endDateDb);

        return view('superadmin.dashboard', compact(
            'totalpenjualan',
            'siteIncomeData',
            'sitesData',
            'totalInvoiceAmount',
            'invoiceDifference',
            'invoiceDifferencePercent',
            'unitsReadyForWO',
            'unitsReadyForPO',
            'unitsInProgress',
            'unitsBelumPO',
            'unitsProsesPO',
            'selectedMonth',
            'monthName',
            'monthlyInvoiceData',
            'currentYear',
            'totalJasaKaryawanAmount',
            'doneJasaKaryawanAmount',
            'jasaKaryawanData',
            'costData',
            'profitMarginData',
            'accountsReceivableData',
            'accountsPayableData',
            'profitMarginPercentage',
            'divisionFilter',
            'siteFilter',
            'startDate',
            'endDate',
            'dateRangeText'
        ));
    }

    /**
     * Calculate site income data
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getSiteIncomeData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Define site targets
        $siteTargets = [
            'PPA' => *********, // IDR 300,000,000
            'IMK' => *********, // IDR 150,000,000
            'UDU' => *********, // IDR 100,000,000
            'TRB' => *********, // IDR 150,000,000
            'PJB' => *********, // IDR 150,000,000
            'DH' => *********, // IDR 150,000,000
        ];

        // Get sites based on filter
        $sitesQuery = Site::where('site_id', '!=', 'WHO');

        // Apply site filter if provided
        if ($siteFilter) {
            $sitesQuery->where('site_id', $siteFilter);
        }

        $sites = $sitesQuery->get();

        $siteIncomeData = [];
        $totalIncome = 0;

        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        // Calculate income for each site
        foreach ($sites as $site) {
            // Get all unit transactions for this site that have invoices
            $query = UnitTransaction::where('site_id', $site->site_id)
                ->whereHas('invoices')
                ->with(['invoices', 'parts']);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }

            // Apply division filter if provided
            if ($divisionFilter) {
                $query->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }

            $unitTransactions = $query->get();

            $siteIncome = 0;

            // Calculate total income from invoices
            foreach ($unitTransactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                            $siteIncome += $part->price * $part->quantity;
                        }
                    } else {
                        $siteIncome += $part->price * $part->quantity;
                    }
                }
            }

            // Add PPN (11%)
            $siteIncome += $siteIncome * 0.11;

            // Get target for this site (default to 0 if not defined)
            // $target = $siteTargets[$site->site_id] ?? 0;
            $target = $siteTargets[$site->site_id] ?? 0;

            // Calculate percentage of target
            $percentageOfTarget = $target > 0 ? ($siteIncome / $target) * 100 : 0;

            // Add to total income
            $totalIncome += $siteIncome;

            // Add to site income data array
            $siteIncomeData[] = [
                'site_id' => $site->site_id,
                'site_name' => $site->site_name,
                'site_address' => $site->address,
                'income' => $siteIncome,
                'target' => $target,
                'percentage_of_target' => $percentageOfTarget,
            ];
        }

        // Calculate percentage of total income for each site
        foreach ($siteIncomeData as &$data) {
            $data['percentage_of_total'] = $totalIncome > 0 ? ($data['income'] / $totalIncome) * 100 : 0;
        }

        return $siteIncomeData;
    }

    /**
     * Calculate total invoice amount for the selected period
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getTotalInvoiceAmount($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }
        $query = Invoice::with(['unitTransactions.parts.part']);
        if ($startDate && $endDate) {
            $query->whereBetween('tanggal_invoice', [$startDate, $endDate]);
            // Tambahkan filter po_date dari unit_transactions
            $query->whereHas('unitTransactions', function ($q) use ($startDate, $endDate) {
                $q->whereBetween('po_date', [$startDate, $endDate]); // KALAU PO TIDAK ADA
            });
        }
        if ($siteFilter) {
            $query->whereHas('unitTransactions', function ($q) use ($siteFilter) {
                $q->where('site_id', $siteFilter);
            });
        }
        if ($divisionFilter) {
            $query->whereHas('unitTransactions.parts.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }
        $invoices = $query->get();
        $totalAmount = 0;
        foreach ($invoices as $invoice) {
            $subtotal = 0;
            foreach ($invoice->unitTransactions as $transaction) {
                if ($siteFilter && $transaction->site_id !== $siteFilter) {
                    continue;
                }
                foreach ($transaction->parts as $part) {
                    $price = $part->price ?? $part->partInventory->price;
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                            $subtotal += $price * $part->quantity;
                        }
                    } else {
                        $subtotal += $price * $part->quantity;
                    }
                }
            }

            // Add PPN
            $ppn = $invoice->ppn ?? 0.11;
            $totalAmount += $subtotal + ($subtotal * $ppn);
        }

        // GetMonthly Reportwith 'done' status for the selected month
        $jasaKaryawanAmount = $this->getJasaKaryawanAmount($date, 'done', $siteFilter);

        // AddMonthly Reportamount to total
        $totalAmount += $jasaKaryawanAmount;
        return $totalAmount;
    }

    /**
     * GetMonthly Reportamount for the selected period and status
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $status
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getJasaKaryawanAmount($date = null, $status = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d');
            }
        } else {
            // Convert datetime format to date format if needed
            if (strpos($startDate, ' ') !== false) {
                $startDate = substr($startDate, 0, 10);
            }
            if (strpos($endDate, ' ') !== false) {
                $endDate = substr($endDate, 0, 10);
            }
        }

        // Build query
        $query = JasaKaryawan::query();

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        }

        // Apply status filter if provided
        if ($status) {
            $query->where('status', $status);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Sum the amount
        return $query->sum('amount');
    }

    /**
     * GetMonthly Reportdata for a specific site and period
     *
     * @param  string  $siteId
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getSiteJasaKaryawanData($siteId, $date = null, $startDate = null, $endDate = null)
    {
        try {
            // Set date range for filtering if not provided
            if (!$startDate || !$endDate) {
                if ($date) {
                    $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
                    $endDate = $date->copy()->endOfMonth()->format('Y-m-d');
                }
            } else {
                // Convert datetime format to date format if needed
                if (strpos($startDate, ' ') !== false) {
                    $startDate = substr($startDate, 0, 10);
                }
                if (strpos($endDate, ' ') !== false) {
                    $endDate = substr($endDate, 0, 10);
                }
            }

            // Get allMonthly Reportfor this site
            $query = JasaKaryawan::where('site_id', $siteId);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $query->whereBetween('date', [$startDate, $endDate]);
            }

            // Get total amount for all statuses
            $totalAmount = (clone $query)->sum('amount');

            // Get total amount for 'done' status
            $doneAmount = (clone $query)->where('status', 'done')->sum('amount');

            // Get counts by status
            $submittedCount = (clone $query)->where('status', 'submitted')->count();
            $approvedCount = (clone $query)->where('status', 'approved')->count();
            $rejectedCount = (clone $query)->where('status', 'rejected')->count();
            $doneCount = (clone $query)->where('status', 'done')->count();

            return [
                'total_amount' => $totalAmount,
                'done_amount' => $doneAmount,
                'submitted_count' => $submittedCount,
                'approved_count' => $approvedCount,
                'rejected_count' => $rejectedCount,
                'done_count' => $doneCount,
                'total_count' => $submittedCount + $approvedCount + $rejectedCount + $doneCount
            ];
        } catch (\Exception $e) {
            Log::error('Error in getSiteJasaKaryawanData for site ' . $siteId . ': ' . $e->getMessage());

            // Return default values
            return [
                'total_amount' => 0,
                'done_amount' => 0,
                'submitted_count' => 0,
                'approved_count' => 0,
                'rejected_count' => 0,
                'done_count' => 0,
                'total_count' => 0
            ];
        }
    }

    /**
     * Calculate remaining invoice amount (unpaid invoices)
     *
     * @return float
     */
    private function getRemainingInvoiceAmount()
    {
        $unpaidInvoices = Invoice::where('payment_status', '!=', 'Lunas')
            ->orWhereNull('payment_status')
            ->with(['unitTransactions.parts'])
            ->get();

        $remainingAmount = 0;

        foreach ($unpaidInvoices as $invoice) {
            $subtotal = 0;

            foreach ($invoice->unitTransactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    $subtotal += $part->price * $part->quantity;
                }
            }

            // Add PPN
            $ppn = $invoice->ppn ?? 0.11;
            $remainingAmount += $subtotal + ($subtotal * $ppn);
        }

        return $remainingAmount;
    }

    /**
     * Calculate cost data for the selected period
     * Currently using dummy data (70% of total invoice amount)
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getCostData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Get total invoice amount for the period with filters
        $totalInvoiceAmount = $this->getTotalInvoiceAmount($date, $divisionFilter, $siteFilter, $startDate, $endDate);

        // For now, use dummy data - 70% of total invoice amount
        $costAmount = $totalInvoiceAmount * 0.7;

        return $costAmount;
    }

    /**
     * Calculate profit margin data for the selected period
     * Sum of all invoices for the period
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getProfitMarginData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        // Get all invoices for the selected month with all relationships
        $query = Invoice::with(['unitTransactions.parts.partInventory.part', 'penawaran.items.partInventory.part', 'manualInvoiceParts.part']);

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            if ($siteFilter === 'WHO') {
                $query->where(function ($q) {
                    $q->whereDoesntHave('unitTransactions')
                        ->orWhereHas('unitTransactions', function ($q) {
                            $q->whereNull('site_id')->orWhere('site_id', 'WHO');
                        });
                });
            } else {
                $query->whereHas('unitTransactions', function ($q) use ($siteFilter) {
                    $q->where('site_id', $siteFilter);
                });
            }
        }

        $invoices = $query->get();

        $totalMargin = 0;

        foreach ($invoices as $invoice) {
            // Use the division-aware calculateInvoiceTotal method to get subtotal
            $totalAmount = $this->calculateInvoiceTotal($invoice, $divisionFilter);

            // Remove PPN to get subtotal (since calculateInvoiceTotal includes PPN)
            $ppn = $invoice->ppn ?? 0.11;
            $subtotal = $totalAmount / (1 + $ppn);

            // Calculate margin (30% of subtotal)
            $margin = $subtotal * 0.3;
            $totalMargin += $margin;
        }

        return $totalMargin;
    }

    /**
     * Calculate accounts receivable data (piutang)
     * Sum of all invoices based on the current date filter
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getAccountsReceivableData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Get total invoice amount for the period with filters
        $totalInvoiceAmount = $this->getTotalInvoiceAmount($date, $divisionFilter, $siteFilter, $startDate, $endDate);

        // Return the actual total invoice amount as piutang
        return $totalInvoiceAmount;
    }

    /**
     * Calculate accounts payable data
     * Currently using dummy data (25% of total invoice amount)
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return float
     */
    private function getAccountsPayableData($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Get total invoice amount for the period with filters
        $totalInvoiceAmount = $this->getTotalInvoiceAmount($date, $divisionFilter, $siteFilter, $startDate, $endDate);

        // For now, use dummy data - 25% of total invoice amount
        $accountsPayable = $totalInvoiceAmount * 0.25;

        return $accountsPayable;
    }

    /**
     * Get monthly invoice data for the entire year, grouped by site
     *
     * @param  int  $year
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return array
     */
    public function getMonthlyInvoiceData($year = null, $divisionFilter = null, $siteFilter = null)
    {
        if (!$year) {
            $year = Carbon::now()->year;
        }

        $monthNames = [];
        $siteMonthlyData = [];
        $siteColors = ['WHO' => 'rgba(235, 49, 36, 0.7)'];

        $sites = Site::all(['site_id', 'site_name', 'address']);
        $colorIndex = 0;
        $baseColors = [
            'rgba(34, 82, 151, 0.7)',
            'rgba(88, 192, 246, 0.7)',
            'rgba(81, 9, 104, 0.7)',
            'rgba(254, 255, 140, 0.7)',
            'rgba(151, 247, 132, 0.7)',
        ];

        foreach ($sites as $site) {
            if ($site->site_id != 'WHO') {
                $siteColors[$site->site_id] = $baseColors[$colorIndex % count($baseColors)];
                $colorIndex++;
            }
        }

        // Initialize month names and site data structures
        for ($month = 1; $month <= 12; $month++) {
            $date = Carbon::createFromDate($year, $month, 1);
            $monthNames[] = $date->locale('id')->translatedFormat('M');
        }

        // Initialize all sites data with zeros
        $siteMonthlyData['WHO'] = [
            'name' => 'PJB - Warehouse',
            'data' => array_fill(1, 12, 0),
            'color' => $siteColors['WHO']
        ];

        foreach ($sites as $site) {
            if (!$siteFilter || $siteFilter === $site->site_id) {
                $siteMonthlyData[$site->site_id] = [
                    'name' => ($site->site_name === 'Warehouse' ? 'PJB' : $site->site_name) . ' - ' . $site->address,
                    'data' => array_fill(1, 12, 0),
                    'color' => $siteColors[$site->site_id] ?? 'rgba(34, 82, 151, 0.7)',
                ];
            }
        }

        $startDate = Carbon::createFromDate($year, 1, 1)->startOfYear()->format('Y-m-d H:i:s');
        $endDate = Carbon::createFromDate($year, 12, 31)->endOfYear()->format('Y-m-d H:i:s');

        $query = Invoice::whereBetween('tanggal_invoice', [$startDate, $endDate])
            ->whereIn('payment_status', ['Lunas', 'Belum Lunas'])
            ->with(['unitTransactions.parts.partInventory.part', 'unitTransactions.site', 'penawaran.items.partInventory.part', 'manualInvoiceParts.part']);

        if ($siteFilter) {
            if ($siteFilter === 'WHO') {
                $query->where(function ($q) {
                    $q->whereDoesntHave('unitTransactions')
                        ->orWhereHas('unitTransactions', function ($q) {
                            $q->whereNull('site_id')->orWhere('site_id', 'WHO');
                        });
                });
            } else {
                $query->whereHas('unitTransactions', function ($q) use ($siteFilter) {
                    $q->where('site_id', $siteFilter);
                });
            }
        }

        // Note: Division filter is now handled in calculateInvoiceTotal method

        $invoices = $query->get();

        foreach ($invoices as $invoice) {
            if (!$invoice->tanggal_invoice) {
                continue;
            }

            $invoiceMonth = Carbon::parse($invoice->tanggal_invoice)->month;

            // Determine which site this invoice belongs to
            $siteId = 'WHO';
            if ($invoice->unitTransactions->isNotEmpty()) {
                $firstTransaction = $invoice->unitTransactions->first();
                $siteId = $firstTransaction->site_id ?? 'WHO';
            }

            // Calculate total amount with division filter applied
            $totalAmount = $this->calculateInvoiceTotal($invoice, $divisionFilter);

            // Only include if there's an amount (meaning parts matched the division filter or no filter applied)
            if ($totalAmount > 0 && (!$siteFilter || $siteFilter === $siteId)) {
                if (isset($siteMonthlyData[$siteId])) {
                    $siteMonthlyData[$siteId]['data'][$invoiceMonth] += $totalAmount;
                }
            }
        }

        $datasets = [];
        foreach ($siteMonthlyData as $siteId => $siteData) {
            if ($siteFilter && $siteId !== $siteFilter) {
                continue;
            }

            $monthlyValues = [];
            for ($month = 1; $month <= 12; $month++) {
                $monthlyValues[] = $siteData['data'][$month] ?? 0;
            }

            $datasets[] = [
                'label' => $siteData['name'],
                'data' => $monthlyValues,
                'backgroundColor' => $siteData['color'],
                'borderColor' => str_replace('0.7', '1', $siteData['color']),
                'borderWidth' => 3,
                'pointBackgroundColor' => str_replace('0.7', '1', $siteData['color']),
                'pointBorderColor' => '#fff',
                'pointBorderWidth' => 2
            ];
        }

        return [
            'labels' => $monthNames,
            'datasets' => $datasets
        ];
    }

    /**
     * Get sites data via AJAX for the dashboard
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSitesData(Request $request)
    {
        try {
            // Log the request for debugging
            Log::info('Sites Data Request', [
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'month' => $request->input('month'),
                'division' => $request->input('division'),
                'site' => $request->input('site')
            ]);

            // Get date range parameters
            $startDateParam = $request->input('start_date');
            $endDateParam = $request->input('end_date');
            $selectedMonth = $request->input('month');

            // Get filter parameters
            $divisionFilter = $request->input('division');
            $siteFilter = $request->input('site');

            // Initialize date variables
            $date = null;
            $startDate = null;
            $endDate = null;

            // Handle date range parameters if provided
            try {
                if ($startDateParam && $endDateParam) {
                    // Parse date range parameters
                    $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
                    $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');

                    // Use the middle date for month-based functions
                    $middleDate = Carbon::parse($startDateParam)->addDays(
                        Carbon::parse($endDateParam)->diffInDays(Carbon::parse($startDateParam)) / 2
                    );
                    $date = $middleDate;
                } else if ($selectedMonth) {
                    // Fallback to month parameter if provided
                    $date = Carbon::createFromFormat('Y-m', $selectedMonth);
                    $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                    $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
                } else {
                    // Default to current month
                    $date = Carbon::now();
                    $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                    $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
                }
            } catch (\Exception $e) {
                Log::error('Error parsing dates: ' . $e->getMessage());
                // Fallback to current month
                $date = Carbon::now();
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }

            // Get site income data for the selected period with filters
            $siteIncomeData = $this->getSiteIncomeData($date, $divisionFilter, $siteFilter, $startDate, $endDate);

            // Get site-specific unit counts with filters
            $siteUnitCounts = $this->getSiteUnitCounts($date, $divisionFilter, $siteFilter, $startDate, $endDate);

            // Get site details for each site
            $siteDetails = [];
            foreach ($siteIncomeData as $incomeData) {
                $siteId = $incomeData['site_id'];

                // Skip if site filter is applied and doesn't match
                if ($siteFilter && $siteFilter !== $siteId) {
                    continue;
                }

                try {
                    // Get repair/pending invoices with filters
                    $repairPendingInvoices = $this->getRepairPendingInvoices($siteId, $startDate, $endDate, $divisionFilter);

                    // Get parts not ready (stock <= min_stock) with filters
                    $partsNotReady = $this->getPartsNotReady($siteId, '', $divisionFilter);

                    // Get pending requests to HO with filters
                    $pendingRequests = $this->getPendingRequests($siteId, $startDate, $endDate, $divisionFilter);

                    // Get best-selling parts by type with filters
                    $bestPartSales = $this->getBestPartSalesByType($siteId, $startDate, $endDate, $divisionFilter);

                    $siteDetails[$siteId] = [
                        'repair_pending_invoices' => $repairPendingInvoices,
                        'part_not_ready' => $partsNotReady,
                        'pending_requests' => $pendingRequests,
                        'best_part_sales' => $bestPartSales
                    ];
                } catch (\Exception $e) {
                    Log::error('Error getting site details for site ' . $siteId . ': ' . $e->getMessage());
                    // Set default values
                    $siteDetails[$siteId] = [
                        'repair_pending_invoices' => ['count' => 0, 'value' => 0],
                        'part_not_ready' => ['count' => 0, 'items' => []],
                        'pending_requests' => ['count' => 0, 'items' => []],
                        'best_part_sales' => []
                    ];
                }
            }

            // Get previous month data for comparison with filters
            try {
                $prevDate = $date->copy()->subMonth();
                $prevStartDate = $prevDate->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $prevEndDate = $prevDate->copy()->endOfMonth()->format('Y-m-d H:i:s');
                $prevSiteIncomeData = $this->getSiteIncomeData($prevDate, $divisionFilter, $siteFilter, $prevStartDate, $prevEndDate);
            } catch (\Exception $e) {
                Log::error('Error getting previous month data: ' . $e->getMessage());
                $prevSiteIncomeData = [];
            }

            // Combine site income data with unit counts and details
            $sitesData = [];
            foreach ($siteIncomeData as $incomeData) {
                $siteId = $incomeData['site_id'];

                // Skip if site filter is applied and doesn't match
                if ($siteFilter && $siteFilter !== $siteId) {
                    continue;
                }

                // Find previous month data for this site
                $prevSiteData = null;
                foreach ($prevSiteIncomeData as $prevData) {
                    if ($prevData['site_id'] === $siteId) {
                        $prevSiteData = $prevData;
                        break;
                    }
                }

                // Calculate trend (percentage change from previous month)
                $trend = 0;
                $trendDirection = 'stable'; // 'up', 'down', or 'stable'

                if ($prevSiteData) {
                    $prevPercentage = $prevSiteData['percentage_of_target'];
                    $currentPercentage = $incomeData['percentage_of_target'];

                    $trend = $currentPercentage - $prevPercentage;

                    if ($trend > 0) {
                        $trendDirection = 'up';
                    } elseif ($trend < 0) {
                        $trendDirection = 'down';
                    }
                }

                try {
                    // Get POs and Invoices data for this site with filters
                    $posAndInvoicesData = $this->getSitePOsAndInvoicesData($siteId, $date, $divisionFilter, $startDate, $endDate);

                    $sitesData[$siteId] = array_merge(
                        $incomeData,
                        $siteUnitCounts[$siteId] ?? [],
                        [
                            'details' => $siteDetails[$siteId] ?? [],
                            'trend' => round($trend, 1),
                            'trend_direction' => $trendDirection,
                            'total_pos_amount' => $posAndInvoicesData['total_pos_amount'],
                            'ready_invoices_amount' => $posAndInvoicesData['ready_invoices_amount'],
                            'total_pos_count' => $posAndInvoicesData['total_pos_count'],
                            'ready_invoices_count' => $posAndInvoicesData['ready_invoices_count'],
                            'invoiced_amount' => $posAndInvoicesData['invoiced_amount'],
                            'invoiced_count' => $posAndInvoicesData['invoiced_count'],
                            'pending_po_amount' => $posAndInvoicesData['pending_po_amount'],
                            'pending_po_count' => $posAndInvoicesData['pending_po_count'],
                            'invoice_percentage' => $posAndInvoicesData['percentage']
                        ]
                    );
                } catch (\Exception $e) {
                    Log::error('Error getting POs and Invoices data for site ' . $siteId . ': ' . $e->getMessage());
                    // Set default values
                    $sitesData[$siteId] = array_merge(
                        $incomeData,
                        $siteUnitCounts[$siteId] ?? [],
                        [
                            'details' => $siteDetails[$siteId] ?? [],
                            'trend' => round($trend, 1),
                            'trend_direction' => $trendDirection,
                            'total_pos_amount' => 0,
                            'ready_invoices_amount' => 0,
                            'total_pos_count' => 0,
                            'ready_invoices_count' => 0,
                            'invoiced_amount' => 0,
                            'invoiced_count' => 0,
                            'pending_po_amount' => 0,
                            'pending_po_count' => 0,
                            'invoice_percentage' => 0
                        ]
                    );
                }
            }

            // Get jasa karyawan data for each site
            foreach ($sitesData as $siteId => &$siteData) {
                try {
                    $jasaKaryawanData = $this->getSiteJasaKaryawanData($siteId, $date, $startDate, $endDate);
                    if (!empty($jasaKaryawanData) && isset($jasaKaryawanData['total_count']) && $jasaKaryawanData['total_count'] > 0) {
                        $siteData['jasa_karyawan'] = $jasaKaryawanData;
                    }
                } catch (\Exception $e) {
                    Log::error('Error getting jasa karyawan data for site ' . $siteId . ': ' . $e->getMessage());
                    // Continue with other sites
                }
            }

            // Get belum PO and proses PO data for each site
            foreach ($sitesData as $siteId => &$siteData) {
                try {
                    $belumPO = UnitTransactionPart::whereHas('unitTransaction', function ($query) use ($siteId, $startDate, $endDate) {
                        $query->where('site_id', $siteId)
                            ->whereBetween('created_at', [$startDate, $endDate])
                            ->where('status', 'belum po');
                    })->count();

                    $belumPOAmount = UnitTransactionPart::whereHas('unitTransaction', function ($query) use ($siteId, $startDate, $endDate) {
                        $query->where('site_id', $siteId)
                            ->whereBetween('created_at', [$startDate, $endDate])
                            ->where('status', 'belum po');
                    })->sum(DB::raw('price * quantity'));

                    $prosesPO = UnitTransactionPart::whereHas('unitTransaction', function ($query) use ($siteId, $startDate, $endDate) {
                        $query->where('site_id', $siteId)
                            ->whereBetween('created_at', [$startDate, $endDate])
                            ->whereIn('status', ['On Process', 'MR', 'Ready WO']);
                    })->count();

                    $prosesPOAmount = UnitTransactionPart::whereHas('unitTransaction', function ($query) use ($siteId, $startDate, $endDate) {
                        $query->where('site_id', $siteId)
                            ->whereBetween('po_date', [$startDate, $endDate]);
                    })->sum(DB::raw('price * quantity'));

                    $siteData['belum_po'] = $belumPO;
                    $siteData['belum_po_amount'] = $belumPOAmount;
                    $siteData['proses_po'] = $prosesPO;
                    $siteData['proses_po_amount'] = $prosesPOAmount;
                } catch (\Exception $e) {
                    Log::error('Error getting PO data for site ' . $siteId . ': ' . $e->getMessage());
                    // Set default values
                    $siteData['belum_po'] = 0;
                    $siteData['belum_po_amount'] = 0;
                    $siteData['proses_po'] = 0;
                    $siteData['proses_po_amount'] = 0;
                }
            }

            // Convert to array for JSON response
            $sitesDataArray = [];
            foreach ($sitesData as $siteData) {
                $sitesDataArray[] = $siteData;
            }

            // Log success
            Log::info('Sites Data Response', [
                'count' => count($sitesDataArray)
            ]);

            return response()->json($sitesDataArray);
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getSitesData: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // Return error response
            return response()->json(['error' => 'Failed to get sites data: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get total units and achievement data for a site
     *
     * @param string $siteId
     * @param \Carbon\Carbon|null $date
     * @param string|null $divisionFilter
     * @param string|null $startDate
     * @param string|null $endDate
     * @return array
     */
    private function getSitePOsAndInvoicesData($siteId, $date = null, $divisionFilter = null, $startDate = null, $endDate = null)
    {

        $siteTargets = [
            'PPA' => *********, // IDR 300,000,000
            'IMK' => *********, // IDR 150,000,000
            'UDU' => *********, // IDR 100,000,000
            'TRB' => *********, // IDR 150,000,000
            'PJB' => *********, // IDR 150,000,000
            'DH' => *********, // IDR 150,000,000
        ];

        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        // 1. Target PO: Get ALL unit transactions for this site with any status (Total Units)
        $totalUnitsQuery = UnitTransaction::where('site_id', $siteId)
            ->with(['parts.partInventory.part']);

        // 2. Apply date filter if provided (by MR date or PO date)
        if ($startDate && $endDate) {
            $totalUnitsQuery->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('mr_date', [$startDate, $endDate])
                    ->orWhereBetween('po_date', [$startDate, $endDate]);
            });
        }

        // 3. Apply division filter only if provided (e.g., 'AC', 'TYRE')
        if (!empty($divisionFilter)) {
            $totalUnitsQuery->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }


        $totalUnitsTransactions = $totalUnitsQuery->get();
        $totalUnitsAmount = 0;
        $totalUnitsAmount = $siteTargets[$siteId] ?? 0;

        // Calculate total amount for all unit transactions
        // foreach ($totalUnitsTransactions as $transaction) {
        //     foreach ($transaction->parts as $part) {
        //         // Get price from part inventory (site-specific) instead of unit transaction part
        //         $price = $part->price ?? $part->partInventory->price;
        //         // If division filter is applied, only count parts of that type
        //         if ($divisionFilter) {
        //             if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type)  === $divisionFilter) {
        //                 $totalUnitsAmount += $price * $part->quantity;
        //             }
        //         } else {
        //             $totalUnitsAmount += $price * $part->quantity;
        //         }
        //     }
        // }

        // 2. Pencapaian Target: Get units with status "Ready PO", "Selesai", "Perbaikan", and "Pending"
        $achievementQuery = UnitTransaction::where('site_id', $siteId)
            ->whereIn('status', ['Ready PO', 'Selesai', 'perbaikan', 'Pending'])
            ->with(['parts.partInventory.part']);

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $achievementQuery->whereBetween('po_date', [$startDate, $endDate]);
        }


        // Calculate total unit transactions based on po date
        $unitTransactions = \App\Models\UnitTransaction::with(['parts'])
            ->where('site_id', $siteId)
            ->whereBetween('po_date', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ])
            ->get();

        // Calculate total based on formula: (total all parts price) * 0.11 + total price
        $achievementAmount = 0;
        foreach ($unitTransactions as $transaction) {
            // Calculate total price for all parts in this transaction
            $transactionTotal = 0;
            foreach ($transaction->parts as $part) {
                // Sum up all part prices * quantities
                $transactionTotal += $part->price * $part->quantity;
            }
            // Apply formula: (total price * 0.11) + total price
            // $achievementAmount += ($transactionTotal * 0.11) + $transactionTotal;
            $achievementAmount += $transactionTotal;
        }

        $readyInvoiceQuery = UnitTransaction::where('site_id', $siteId)
            ->whereHas('invoices', function ($q) use ($startDate, $endDate) {
                if ($startDate && $endDate) {
                    $q->whereBetween('tanggal_invoice', [$startDate, $endDate]);
                }
            })
            ->with(['parts.partInventory.part']);

        // Filter berdasarkan PO date juga (opsional, kalau memang ingin kedua-duanya)
        if ($startDate && $endDate) {
            $readyInvoiceQuery->whereBetween('po_date', [$startDate, $endDate]);
        }

        // Filter division jika ada
        if ($divisionFilter) {
            $readyInvoiceQuery->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        $readyInvoiceTransactions = $readyInvoiceQuery->get();
        $readyInvoiceAmount = 0;

        // Calculate total amount for ready invoice transactions
        foreach ($readyInvoiceTransactions as $transaction) {
            foreach ($transaction->parts as $part) {
                // Get price from part inventory (site-specific) instead of unit transaction part
                $price = $part->price ?? $part->partInventory->part->price;
                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                        $readyInvoiceAmount += $price * $part->quantity;
                    }
                } else {
                    $readyInvoiceAmount += $price * $part->quantity;
                }
            }
        }

        // 4. Pending PO: Get units with status "Ready WO", "MR", and "On Process"
        $pendingPOQuery = UnitTransaction::where('site_id', $siteId)
            ->whereIn('status', ['Ready WO', 'MR', 'On Process', 'Pending'])
            ->with(['parts.partInventory.part']);

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $pendingPOQuery->whereBetween('mr_date', [$startDate, $endDate]);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $pendingPOQuery->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        $pendingPOTransactions = $pendingPOQuery->get();
        $pendingPOAmount = 0;

        // Calculate total amount for pending PO transactions
        foreach ($pendingPOTransactions as $transaction) {
            foreach ($transaction->parts as $part) {
                // Get price from part inventory (site-specific) instead of unit transaction part
                $price = $part->price ?? $part->partInventory->price;

                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                        $pendingPOAmount += $price * $part->quantity;
                    }
                } else {
                    $pendingPOAmount += $price * $part->quantity;
                }
            }
        }

        // Get Monthly Report data for this site
        try {
            $jasaKaryawanData = $this->getSiteJasaKaryawanData($siteId, $date, $startDate, $endDate);

            // Ensure jasaKaryawanData has the expected structure
            if (!isset($jasaKaryawanData['total_amount'])) {
                $jasaKaryawanData['total_amount'] = 0;
            }
            if (!isset($jasaKaryawanData['done_amount'])) {
                $jasaKaryawanData['done_amount'] = 0;
            }

            // Add Monthly Report 'total' amount to total units amount (target)
            $totalUnitsAmount += $jasaKaryawanData['total_amount'];

            // Add Monthly Report 'done' amount to achievement amount
            $achievementAmount += $jasaKaryawanData['done_amount'];
        } catch (\Exception $e) {
            Log::error('Error getting jasa karyawan data for site ' . $siteId . ': ' . $e->getMessage());
            // Don't add anything to the totals
            $jasaKaryawanData = [
                'total_amount' => 0,
                'done_amount' => 0,
                'total_count' => 0,
                'done_count' => 0
            ];
        }

        // Calculate percentage based on achievement amount vs total amount
        // Progress percentage = (Pencapaian Target Amount) / (Target PO Amount) * 100
        $percentage = $totalUnitsAmount > 0 ? ($achievementAmount / $totalUnitsAmount) * 100 : 0;

        return [
            'total_pos_amount' => $totalUnitsAmount,           // Target PO (all units)
            'ready_invoices_amount' => $achievementAmount,     // Pencapaian Target (Ready PO + Selesai + Perbaikan + Pending)
            'total_pos_count' => $totalUnitsTransactions->count(),
            'ready_invoices_count' => $unitTransactions->count(),
            'invoiced_amount' => $readyInvoiceAmount,          // Ready Invoice (units with invoices)
            'invoiced_count' => $readyInvoiceTransactions->count(),
            'pending_po_amount' => $pendingPOAmount,           // Pending PO (Ready WO + On Progress + Proses MR)
            'pending_po_count' => $pendingPOTransactions->count(),
            'percentage' => $percentage,
            'jasa_karyawan' => $jasaKaryawanData
        ];
    }

    /**
     * Get site-specific unit status counts
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getSiteUnitCounts($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Get sites based on filter
        $sitesQuery = Site::where('site_id', '!=', 'WHO');

        // Apply site filter if provided
        if ($siteFilter) {
            $sitesQuery->where('site_id', $siteFilter);
        }

        $sites = $sitesQuery->get();
        $siteUnitCounts = [];

        // Set date range for filtering if not provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        foreach ($sites as $site) {
            $query = UnitTransaction::where('site_id', $site->site_id);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $query->whereBetween('po_date', [$startDate, $endDate]);
            }

            // Apply division filter if provided
            if ($divisionFilter) {
                $divisionFilterQuery = function ($q) use ($divisionFilter) {
                    $q->whereHas('parts.partInventory.part', function ($q2) use ($divisionFilter) {
                        $q2->where('part_type', $divisionFilter);
                    });
                };

                $query->where(function ($q) use ($divisionFilterQuery) {
                    $divisionFilterQuery($q);
                });
            }

            $readyWOQuery = clone $query;
            $readyWOQuery->where('status', 'Ready WO');
            if ($divisionFilter) {
                $readyWOQuery->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }
            $readyWO = $readyWOQuery->count();

            $readyPOQuery = clone $query;
            $readyPOQuery->where('status', 'Ready PO');
            if ($divisionFilter) {
                $readyPOQuery->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }
            $readyPO = $readyPOQuery->count();

            $inProgressQuery = clone $query;
            $inProgressQuery->where('status', 'On Process');
            if ($divisionFilter) {
                $inProgressQuery->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }
            $inProgress = $inProgressQuery->count();

            // Get "Pending PO" transactions (status "Ready WO", "MR", and "On Process")
            $pendingPOQuery = (clone $query)->whereIn('status', ['Ready WO', 'MR', 'On Process']);

            // Apply date filter if provided
            if ($startDate && $endDate) {
                // $pendingPOQuery->whereBetween('mr_date', [$startDate, $endDate]);
            }

            // Apply division filter if provided
            if ($divisionFilter) {
                $pendingPOQuery->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                    $q->where('part_type', $divisionFilter);
                });
            }

            $pendingPOTransactions = $pendingPOQuery->with(['parts.partInventory.part'])->get();

            $pendingPOCount = $pendingPOTransactions->count();
            $pendingPOAmount = 0;

            foreach ($pendingPOTransactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    // Get price from part inventory (site-specific) instead of unit transaction part
                    $price = $part->price ?? $part->partInventory->price;

                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                            $pendingPOAmount += $price * $part->quantity;
                        }
                    } else {
                        $pendingPOAmount += $price * $part->quantity;
                    }
                }
            }
            // Get "Ready Invoice" transactions (units that have invoices)
            $readyInvoiceQuery = UnitTransaction::where('site_id', $site->site_id)
                ->whereHas('invoices', function ($query) use ($startDate, $endDate) {
                    $query->whereBetween('tanggal_invoice', [$startDate, $endDate]);
                });
            // ->whereBetween('do_date', [$startDate, $endDate]); // Tambahan filter do_date


            // Apply date filter if provided
            // if ($startDate && $endDate) {
            //     $readyInvoiceQuery->whereBetween('created_at', [$startDate, $endDate]);
            // }
            $readyInvoiceQuery->whereHas('invoices', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('tanggal_invoice', [$startDate, $endDate]);
            });

            // Apply division filter if provided
            $readyInvoiceTransactions = $readyInvoiceQuery->with(['parts.partInventory.part'])->get();
            $readyInvoiceCount = $readyInvoiceTransactions->count();
            $readyInvoiceAmount = 0;

            foreach ($readyInvoiceTransactions as $transaction) {
                foreach ($transaction->parts as $part) {
                    // Get price from part inventory (site-specific) instead of unit transaction part
                    $price = $part->price ?? $part->partInventory->price;
                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                            $readyInvoiceAmount += $price * $part->quantity;
                        }
                    } else {
                        $readyInvoiceAmount += $price * $part->quantity;
                    }
                }
            }
            $readyInvoiceAmount = $readyInvoiceAmount + $readyInvoiceAmount * 11 / 100;

            $siteUnitCounts[$site->site_id] = [
                'site_id' => $site->site_id,
                'site_name' => $site->site_name,
                'ready_wo' => $readyWO,
                'ready_po' => $readyPO,
                'in_progress' => $inProgress,
                'pending_po_count' => $pendingPOCount,
                'pending_po_amount' => $pendingPOAmount,
                'ready_invoice_count' => $readyInvoiceCount,
                'ready_invoice_amount' => $readyInvoiceAmount,
                // Keep old names for backward compatibility
                'belum_po' => $pendingPOCount,
                'belum_po_amount' => $pendingPOAmount,
                'proses_po' => $readyInvoiceCount,
                'proses_po_amount' => $readyInvoiceAmount,
                'invoiced_count' => $readyInvoiceCount,
                'invoiced_amount' => $readyInvoiceAmount,
                'total' => $readyWO + $readyPO + $inProgress
            ];
        }

        return $siteUnitCounts;
    }

    /**
     * Count units ready for Work Order (WO)
     *
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return int
     */
    private function getUnitsReadyForWO($divisionFilter = null, $siteFilter = null)
    {
        $query = UnitTransaction::where('status', 'Ready WO');

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        return $query->count();
    }

    /**
     * Count units ready for Purchase Order (PO)
     *
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return int
     */
    private function getUnitsReadyForPO($divisionFilter = null, $siteFilter = null)
    {
        $query = UnitTransaction::where('status', 'Ready PO');

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        return $query->count();
    }

    /**
     * Count units in progress (unit transactions)
     *
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return int
     */
    private function getUnitsInProgress($divisionFilter = null, $siteFilter = null)
    {
        $query = UnitTransaction::where('status', 'On Process');

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        return $query->count();
    }

    /**
     * Get units with "Belum PO" status (all statuses except "Ready PO", "Selesai", "Pending", "Perbaikan")
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getUnitsBelumPO($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Create query for unit transactions with statuses other than the specified ones
        $query = UnitTransaction::whereNotIn('status', ['Ready PO', 'Selesai', 'Pending', 'perbaikan']);

        // Apply date filter if provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        // Get transactions with their parts
        $transactions = $query->with(['parts.partInventory.part'])->get();

        // Calculate total amount
        $totalAmount = 0;
        foreach ($transactions as $transaction) {
            foreach ($transaction->parts as $part) {
                // Get price from part inventory (site-specific) instead of unit transaction part
                $price = $part->price ?? $part->partInventory->price;

                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                        $totalAmount += $price * $part->quantity;
                    }
                } else {
                    $totalAmount += $price * $part->quantity;
                }
            }
        }

        return [
            'count' => $transactions->count(),
            'amount' => $totalAmount
        ];
    }

    /**
     * Get units with "Proses Invoice" status ("Ready PO", "Pending", "Perbaikan") - excluding "Selesai"
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getUnitsProsesPO($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        // Create query for unit transactions with the specified statuses
        // Exclude "Selesai" status as requested
        $query = UnitTransaction::whereIn('status', ['Ready PO', 'Pending', 'perbaikan']);

        // Apply date filter if provided
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Apply site filter if provided
        if ($siteFilter) {
            $query->where('site_id', $siteFilter);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        // Get transactions with their parts
        $transactions = $query->with(['parts.partInventory.part'])->get();

        // Calculate total amount
        $totalAmount = 0;
        foreach ($transactions as $transaction) {
            foreach ($transaction->parts as $part) {
                // Get price from part inventory (site-specific) instead of unit transaction part
                $price = $part->price ?? $part->partInventory->price;

                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                        $totalAmount += $price * $part->quantity;
                    }
                } else {
                    $totalAmount += $price * $part->quantity;
                }
            }
        }

        return [
            'count' => $transactions->count(),
            'amount' => $totalAmount
        ];
    }

    /**
     * Get detailed information for a specific site
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $siteId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSiteDetails(Request $request, $siteId)
    {
        // Get date range parameters
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month');

        // Get filter parameters
        $divisionFilter = $request->input('division');

        // Initialize date variables
        $date = null;
        $startDate = null;
        $endDate = null;

        // Handle date range parameters if provided
        if ($startDateParam && $endDateParam) {
            // Parse date range parameters
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');

            // Use the middle date for month-based functions
            $middleDate = Carbon::parse($startDateParam)->addDays(
                Carbon::parse($endDateParam)->diffInDays(Carbon::parse($startDateParam)) / 2
            );
            $date = $middleDate;
        } else if ($selectedMonth) {
            // Fallback to month parameter if provided
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        } else {
            // Default to current month
            $date = Carbon::now();
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        }

        // 1. Get repair/pending invoices with filters
        $repairPendingInvoices = $this->getRepairPendingInvoices($siteId, $startDate, $endDate, $divisionFilter);

        // 2. Get parts not ready (stock <= min_stock) with filters
        $partsNotReady = $this->getPartsNotReady($siteId, '', $divisionFilter);

        // 3. Get pending requests to HO with filters
        $pendingRequests = $this->getPendingRequests($siteId, $startDate, $endDate, $divisionFilter);

        // 4. Get best-selling parts by type with filters
        $bestPartSales = $this->getBestPartSalesByType($siteId, $startDate, $endDate, $divisionFilter);

        return response()->json([
            'repair_pending_invoices' => $repairPendingInvoices,
            'part_not_ready' => $partsNotReady,
            'pending_requests' => $pendingRequests,
            'best_part_sales' => $bestPartSales,
            'division_filter' => $divisionFilter
        ]);
    }

    /**
     * Get repair/pending invoices for a site
     *
     * @param  string  $siteId
     * @param  string  $startDate
     * @param  string  $endDate
     * @param  string|null  $divisionFilter
     * @return array
     */
    private function getRepairPendingInvoices($siteId, $startDate, $endDate, $divisionFilter = null)
    {
        try {
            // Get unit transactions with repair or pending status
            $query = UnitTransaction::where('site_id', $siteId)
                ->whereIn('status', ['repair', 'pending'])
                ->whereBetween('created_at', [$startDate, $endDate]);

            // Apply division filter if provided
            if ($divisionFilter) {
                $query->whereHas('parts', function ($q) use ($divisionFilter) {
                    $q->whereHas('partInventory', function ($q2) use ($divisionFilter) {
                        $q2->whereHas('part', function ($q3) use ($divisionFilter) {
                            $q3->where('part_type', $divisionFilter);
                        });
                    });
                });
            }

            $transactions = $query->with(['invoices', 'parts.partInventory.part'])->get();

            $totalValue = 0;

            foreach ($transactions as $transaction) {
                $transactionTotal = 0;

                // Get parts from UnitTransactionPart model with part inventory and part
                $parts = UnitTransactionPart::where('unit_transaction_id', $transaction->id)
                    ->with(['partInventory.part'])
                    ->get();

                foreach ($parts as $part) {
                    // Get price from part inventory (site-specific) instead of unit transaction part
                    $price = $part->price ?? $part->partInventory->price;

                    // If division filter is applied, only count parts of that type
                    if ($divisionFilter) {
                        // Eager load the part relationship to avoid N+1 query problem
                        if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                            $transactionTotal += $price * $part->quantity;
                        }
                    } else {
                        $transactionTotal += $price * $part->quantity;
                    }
                }

                // Add PPN if applicable
                if ($transaction->invoices->isNotEmpty()) {
                    $ppn = $transaction->invoices->first()->ppn ?? 0.11;
                    $transactionTotal *= (1 + $ppn); // Multiply by (1 + ppn) instead of adding
                } else {
                    $transactionTotal *= 1.11; // Default PPN (multiply by 1.11 for 11%)
                }

                $totalValue += $transactionTotal;
            }

            return [
                'count' => $transactions->count(),
                'value' => $totalValue
            ];
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getRepairPendingInvoices: ' . $e->getMessage());

            // Return empty data
            return [
                'count' => 0,
                'value' => 0
            ];
        }
    }

    /**
     * Get parts that are not ready (stock < average of min and max stock)
     *
     * @param  string  $siteId
     * @param  string  $search
     * @param  string|null  $divisionFilter
     * @return array
     */
    private function getPartsNotReady($siteId, $search = '', $divisionFilter = null)
    {
        // Base query
        $query = DB::table('part_inventories')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->where('part_inventories.site_id', $siteId);

        // Apply search filter if provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('parts.part_name', 'like', "%{$search}%")
                    ->orWhere('parts.part_code', 'like', "%{$search}%")
                    ->orWhere('part_inventories.site_part_name', 'like', "%{$search}%");
            });
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->where('parts.part_type', $divisionFilter);
        }
        // Apply new rules for not ready parts:
        // 1. Stock < average(min_stock, max_stock)
        // 2. Ignore parts where both min_stock and max_stock are 0 or null
        $query->whereRaw('part_inventories.stock_quantity < (part_inventories.min_stock + part_inventories.max_stock)/2')
            ->where(function ($query) {
                $query->where('part_inventories.min_stock', '>', 0)
                    ->orWhere('part_inventories.max_stock', '>', 0);
            }); // Only include parts with either min_stock or max_stock > 0

        $parts = $query->select(
            'parts.part_code',
            'parts.part_name',
            'parts.part_type',
            'part_inventories.stock_quantity as stock',
            'part_inventories.min_stock',
            'part_inventories.priority',
            'part_inventories.max_stock'
        )
            ->get();

        return [
            'count' => $parts->count(),
            'items' => $parts
        ];
    }

    /**
     * Get pending requests to HO
     *
     * @param  string  $siteId
     * @param  string  $startDate
     * @param  string  $endDate
     * @param  string|null  $divisionFilter
     * @return array
     */
    private function getPendingRequests($siteId, $startDate, $endDate, $divisionFilter = null)
    {
        try {
            // Get requisitions that are not completed
            $query = DB::table('requisitions')
                ->where('requisitions.site_id', $siteId)
                ->where('requisitions.status', '!=', 'selesai')
                ->whereBetween('requisitions.created_at', [$startDate, $endDate])
                ->join('requisition_details', 'requisitions.requisition_id', '=', 'requisition_details.requisition_id')
                ->join('parts', 'requisition_details.part_code', '=', 'parts.part_code');

            // Apply division filter if provided
            if ($divisionFilter) {
                $query->where('parts.part_type', $divisionFilter);
            }

            $requisitions = $query->select(
                'requisitions.requisition_id as request_id',
                'parts.part_name',
                'parts.part_type',
                'requisition_details.quantity',
                'requisitions.status'
            )
                ->get();

            return [
                'count' => $requisitions->count(),
                'items' => $requisitions
            ];
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getPendingRequests: ' . $e->getMessage());

            // Return empty data
            return [
                'count' => 0,
                'items' => collect([])
            ];
        }
    }

    /**
     * Get best-selling parts by type (AC, TYRE, FABRIKASI)
     *
     * @param  string  $siteId
     * @param  string  $startDate
     * @param  string  $endDate
     * @param  string|null  $divisionFilter
     * @return array
     */
    private function getBestPartSalesByType($siteId, $startDate, $endDate, $divisionFilter = null)
    {
        try {
            // Define the part types we want to track
            $partTypes = Part::distinct()->pluck('part_type')->toArray();

            // Filter out empty part types
            $partTypes = array_filter($partTypes, function ($type) {
                return !empty($type);
            });

            // If no part types found, use default ones
            if (empty($partTypes)) {
                $partTypes = ['AC', 'TYRE', 'FABRIKASI', 'PERLENGKAPAN AC', 'PERSEDIAAN LAINNYA'];
            }

            // If division filter is applied, only show that division
            if ($divisionFilter) {
                $partTypes = [$divisionFilter];
            }

            $result = [];

            foreach ($partTypes as $partType) {
                // Get top 5 best-selling parts for this type
                $topParts = DB::table('unit_transaction_parts')
                    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                    ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                    ->where('unit_transactions.site_id', $siteId)
                    ->where('parts.part_type', $partType)
                    ->whereBetween('unit_transactions.created_at', [$startDate, $endDate])
                    ->select(
                        'parts.part_code',
                        'parts.part_name',
                        'parts.part_type',
                        DB::raw('SUM(unit_transaction_parts.quantity) as total_quantity'),
                        DB::raw('SUM(unit_transaction_parts.price * unit_transaction_parts.quantity) as total_value'),
                        DB::raw('AVG(unit_transaction_parts.price) as avg_price')
                    )
                    ->groupBy('parts.part_code', 'parts.part_name', 'parts.part_type')
                    ->orderByDesc('total_value') // Order by total value instead of quantity
                    ->limit(5) // Limit to top 5 best-selling parts
                    ->get();

                // Calculate total revenue for this part type
                $totalTypeRevenue = $topParts->sum('total_value');

                // Add PPN (11%)
                $totalTypeRevenueWithPPN = $totalTypeRevenue * 1.11;

                // Calculate percentage contribution for each part
                foreach ($topParts as $part) {
                    $part->contribution_percent = $totalTypeRevenue > 0
                        ? round(($part->total_value / $totalTypeRevenue) * 100, 1)
                        : 0;
                }

                $result[$partType] = [
                    'count' => $topParts->count(),
                    'items' => $topParts,
                    'total_revenue' => $totalTypeRevenue,
                    'total_revenue_with_ppn' => $totalTypeRevenueWithPPN
                ];
            }

            return $result;
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getBestPartSalesByType: ' . $e->getMessage());

            // Return empty data
            $emptyResult = [];
            foreach ($partTypes as $partType) {
                $emptyResult[$partType] = [
                    'count' => 0,
                    'items' => collect([]),
                    'total_revenue' => 0,
                    'total_revenue_with_ppn' => 0
                ];
            }
            return $emptyResult;
        }
    }

    /**
     * Get best-selling parts across all sites by type
     *
     * @param  string  $startDate
     * @param  string  $endDate
     * @param  string|null  $divisionFilter
     * @param  string|null  $siteFilter
     * @return array
     */
    private function getBestPartsAcrossAllSites($startDate, $endDate, $divisionFilter = null, $siteFilter = null)
    {
        try {
            // Get settings from session or use defaults
            $limit = session('best_parts_limit', 5);
            $sortBy = session('best_parts_sort_by', 'value');

            // Define the specific part types we want to track (only these three)
            $partTypes = ['AC', 'TYRE', 'FABRIKASI'];

            // If division filter is applied, only show that division if it's one of the allowed types
            if ($divisionFilter) {
                $divisionFilter = strtoupper($divisionFilter);
                if (in_array($divisionFilter, $partTypes)) {
                    $partTypes = [$divisionFilter];
                } else {
                    // If invalid division filter, return empty results
                    $emptyResult = [];
                    foreach (['AC', 'TYRE', 'FABRIKASI'] as $type) {
                        $emptyResult[$type] = [
                            'count' => 0,
                            'items' => collect([]),
                            'total_revenue' => 0,
                            'total_revenue_with_ppn' => 0
                        ];
                    }
                    return $emptyResult;
                }
            }

            $result = [];

            foreach ($partTypes as $partType) {
                // Get top parts for this type across all sites based on settings
                $query = DB::table('unit_transaction_parts')
                    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                    ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                    ->join('sites', 'unit_transactions.site_id', '=', 'sites.site_id')
                    ->where('parts.part_type', $partType)
                    ->whereBetween('unit_transactions.created_at', [$startDate, $endDate]);

                // Apply site filter if provided
                if ($siteFilter) {
                    $query->where('unit_transactions.site_id', $siteFilter);
                }

                $query->select(
                    'parts.part_code',
                    'parts.part_name',
                    'parts.part_type',
                    DB::raw('SUM(unit_transaction_parts.quantity) as total_quantity'),
                    DB::raw('SUM(unit_transaction_parts.price * unit_transaction_parts.quantity) as total_value'),
                    DB::raw('AVG(unit_transaction_parts.price) as avg_price'),
                    DB::raw('COUNT(DISTINCT unit_transactions.site_id) as site_count')
                )
                    ->groupBy('parts.part_code', 'parts.part_name', 'parts.part_type');

                // Apply sorting based on settings
                if ($sortBy === 'quantity') {
                    $query->orderByDesc('total_quantity');
                } else {
                    $query->orderByDesc('total_value');
                }

                // Get top parts for display (limited)
                $topParts = $query->limit($limit)->get();

                // Calculate total revenue for ALL parts in this division (not just top 5)
                // Create a separate query without limit to get the true total
                $totalRevenueQuery = DB::table('unit_transaction_parts')
                    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                    ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                    ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                    ->where('parts.part_type', $partType)
                    ->whereBetween('unit_transactions.created_at', [$startDate, $endDate]);

                // Apply site filter if provided
                if ($siteFilter) {
                    $totalRevenueQuery->where('unit_transactions.site_id', $siteFilter);
                }

                $totalTypeRevenue = $totalRevenueQuery->sum(DB::raw('unit_transaction_parts.price * unit_transaction_parts.quantity'));

                // Add PPN (11%)
                $totalTypeRevenueWithPPN = $totalTypeRevenue * 1.11;

                // Calculate percentage contribution for each part
                foreach ($topParts as $part) {
                    $part->contribution_percent = $totalTypeRevenue > 0
                        ? round(($part->total_value / $totalTypeRevenue) * 100, 1)
                        : 0;
                }

                $result[$partType] = [
                    'count' => $topParts->count(),
                    'items' => $topParts,
                    'total_revenue' => $totalTypeRevenue,
                    'total_revenue_with_ppn' => $totalTypeRevenueWithPPN
                ];
            }

            // Ensure all three part types are always present in the result
            $finalResult = [];
            foreach (['AC', 'TYRE', 'FABRIKASI'] as $type) {
                $finalResult[$type] = $result[$type] ?? [
                    'count' => 0,
                    'items' => collect([]),
                    'total_revenue' => 0,
                    'total_revenue_with_ppn' => 0
                ];
            }

            return $finalResult;
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getBestPartsAcrossAllSites: ' . $e->getMessage());

            // Return empty data for all three part types
            $emptyResult = [];
            foreach (['AC', 'TYRE', 'FABRIKASI'] as $partType) {
                $emptyResult[$partType] = [
                    'count' => 0,
                    'items' => collect([]),
                    'total_revenue' => 0,
                    'total_revenue_with_ppn' => 0
                ];
            }
            return $emptyResult;
        }
    }

    /**
     * Save best parts settings to session
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveBestPartsSettings(Request $request)
    {
        // Validate request
        $request->validate([
            'limit' => 'required|integer|min:1|max:20',
            'sort_by' => 'required|in:value,quantity',
        ]);

        // Save settings to session
        session(['best_parts_limit' => $request->limit]);
        session(['best_parts_sort_by' => $request->sort_by]);

        return response()->json([
            'success' => true,
            'message' => 'Pengaturan berhasil disimpan',
        ]);
    }

    /**
     * Get best parts data for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBestPartsData(Request $request)
    {
        // Get date range parameters
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month');
        $divisionFilter = $request->input('division');
        $siteFilter = $request->input('site');

        // Initialize date variables
        $startDate = null;
        $endDate = null;

        // Handle date range parameters if provided
        if ($startDateParam && $endDateParam) {
            // Parse date range parameters
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');
        } else if ($selectedMonth) {
            // Fallback to month parameter if provided
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        } else {
            // Default to current month
            $date = Carbon::now();
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        }

        // Normalize division filter (convert to uppercase for consistency)
        if ($divisionFilter) {
            $divisionFilter = strtoupper($divisionFilter);
        }

        // Get best parts across all sites with filters
        $bestPartsAllSites = $this->getBestPartsAcrossAllSites($startDate, $endDate, $divisionFilter, $siteFilter);

        // Log the request parameters and date range for debugging
        Log::info('Best Parts Data Request', [
            'start_date' => $startDateParam,
            'end_date' => $endDateParam,
            'month' => $selectedMonth,
            'division' => $divisionFilter,
            'site' => $siteFilter,
            'used_start_date' => $startDate,
            'used_end_date' => $endDate
        ]);

        return response()->json($bestPartsAllSites);
    }

    /**
     * Get units with specific status for dashboard modal
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUnitsByStatus(Request $request, $status)
    {
        // Get selected date range
        $startDateInput = $request->input('start_date');
        $endDateInput = $request->input('end_date');
        $siteId = $request->input('site_id');

        // Set default date range if not provided
        if (!$startDateInput || !$endDateInput) {
            $now = Carbon::now();
            $startDate = $now->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $now->format('Y-m-d H:i:s');
        } else {
            $startDate = Carbon::createFromFormat('Y-m-d', $startDateInput)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::createFromFormat('Y-m-d', $endDateInput)->endOfDay()->format('Y-m-d H:i:s');
        }

        // Build the query based on the status parameter
        if ($status === 'belum-po') {
            // Get units with "Pending PO" status - EXACTLY the same as card calculation
            // Status: Ready WO, MR, On Process
            $query = UnitTransaction::whereIn('status', ['Ready WO', 'MR', 'On Process']);
        } elseif ($status === 'proses-invoice') {
            // Get units with "Ready Invoice" status (units that have invoices)
            $query = UnitTransaction::whereHas('invoices');
        } else {
            return response()->json(['error' => 'Invalid status parameter'], 400);
        }

        // Apply date filter
        $query->whereBetween('created_at', [$startDate, $endDate]);

        // Apply site filter if provided
        if ($siteId) {
            $query->where('site_id', $siteId);
        }

        // Get transactions with their parts and unit
        $transactions = $query->with(['parts.partInventory.part', 'unit'])->get();

        // Format the data for the response
        $formattedTransactions = $transactions->map(function ($transaction) {
            // Calculate total price for this transaction
            $totalPrice = 0;

            // Check if unit exists
            $unitName = 'Unknown Unit';
            $unitCode = 'Unknown Code';

            if ($transaction->unit) {
                $unitName = $transaction->unit->unit_type ?? 'Unknown Unit';
                $unitCode = $transaction->unit->unit_code ?? 'Unknown Code';
            }

            // Format parts data for detail view
            $formattedParts = collect();

            if ($transaction->parts && $transaction->parts->count() > 0) {
                $formattedParts = $transaction->parts->map(function ($part) {
                    $partName = 'Unknown Part';
                    $partCode = 'Unknown Code';

                    if ($part->partInventory && $part->partInventory->part) {
                        $partName = $part->partInventory->part->part_name ?? 'Unknown Part';
                        $partCode = $part->partInventory->part->part_code ?? 'Unknown Code';
                    }

                    $price = $part->price ?? 0;
                    $quantity = $part->quantity ?? 0;
                    $total = $price * $quantity;

                    return [
                        'id' => $part->id,
                        'part_name' => $partName,
                        'part_code' => $partCode,
                        'quantity' => $quantity,
                        'price' => $price,
                        'total' => $total
                    ];
                });

                // Calculate total price
                foreach ($transaction->parts as $part) {
                    $price = $part->price ?? 0;
                    $quantity = $part->quantity ?? 0;
                    $totalPrice += $price * $quantity;
                }
            }

            return [
                'id' => $transaction->id,
                'unit_name' => $unitName,
                'unit_code' => $unitCode,
                'total_price' => $totalPrice,
                'status' => $transaction->status,
                'notes' => $transaction->remarks ?? '-',
                'created_at' => $transaction->created_at->format('d-m-Y H:i'),
                'updated_at' => $transaction->updated_at->format('d-m-Y H:i'),
                'attachment_path' => $transaction->attachment_path,
                'parts' => $formattedParts
            ];
        });

        return response()->json([
            'status' => $status,
            'transactions' => $formattedTransactions
        ]);
    }

    /**
     * Get detailed division parts data for modal display
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDivisionPartsDetail(Request $request)
    {
        try {
            // Get parameters
            $startDateParam = $request->input('start_date');
            $endDateParam = $request->input('end_date');
            $selectedMonth = $request->input('month');
            $divisionFilter = $request->input('division');
            $siteFilter = $request->input('site');

            // Log the incoming request parameters for debugging
            Log::info('getDivisionPartsDetail called with parameters:', [
                'start_date' => $startDateParam,
                'end_date' => $endDateParam,
                'month' => $selectedMonth,
                'division' => $divisionFilter,
                'site' => $siteFilter
            ]);

            // Initialize date variables
            $startDate = null;
            $endDate = null;

            // Handle date range parameters if provided
            if ($startDateParam && $endDateParam) {
                $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
                $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');
            } elseif ($selectedMonth) {
                // Parse month parameter (format: YYYY-MM)
                $monthDate = Carbon::createFromFormat('Y-m', $selectedMonth);
                $startDate = $monthDate->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $monthDate->copy()->endOfMonth()->format('Y-m-d H:i:s');
            } else {
                // Default to current month
                $currentDate = Carbon::now();
                $startDate = $currentDate->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $currentDate->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }

            // Normalize division filter
            if ($divisionFilter) {
                $divisionFilter = strtoupper($divisionFilter);
            }

            // Log the processed date range and filters
            Log::info('Processed parameters:', [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'division_filter' => $divisionFilter,
                'site_filter' => $siteFilter
            ]);

            // Validate division filter - only allow AC, TYRE, FABRIKASI
            if ($divisionFilter && !in_array($divisionFilter, ['AC', 'TYRE', 'FABRIKASI'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid division filter. Only AC, TYRE, and FABRIKASI are allowed.',
                    'data' => [],
                    'summary' => [
                        'total_items' => 0,
                        'total_quantity' => 0,
                        'total_value' => 0,
                        'total_value_with_ppn' => 0,
                        'division' => $divisionFilter
                    ]
                ]);
            }

            // Query to get actual unit transaction data for the selected division
            $query = DB::table('unit_transaction_parts')
                ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
                ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
                ->join('sites', 'unit_transactions.site_id', '=', 'sites.site_id')
                ->whereBetween('unit_transactions.created_at', [$startDate, $endDate]);

            // Apply division filter if provided
            if ($divisionFilter) {
                $query->where('parts.part_type', $divisionFilter);
            }

            // Apply site filter if provided
            if ($siteFilter) {
                $query->where('unit_transactions.site_id', $siteFilter);
            }

            // Get transaction data
            $transactionData = $query->select(
                'parts.part_code',
                'parts.part_name',
                'parts.part_type',
                'part_inventories.site_part_name',
                'unit_transaction_parts.quantity',
                'unit_transaction_parts.price',
                DB::raw('(unit_transaction_parts.quantity * unit_transaction_parts.price) as total_value'),
                'sites.site_name',
                'sites.site_id',
                'unit_transactions.created_at as transaction_date',
                'unit_transactions.wo_number',
                'unit_transactions.status as transaction_status'
            )
                ->orderBy('unit_transactions.created_at', 'desc')
                ->get();

            // Log the number of records found
            Log::info('Transaction query results:', [
                'total_records' => $transactionData->count(),
                'division_filter' => $divisionFilter,
                'site_filter' => $siteFilter,
                'date_range' => [$startDate, $endDate]
            ]);

            // Calculate totals
            $totalQuantity = $transactionData->sum('quantity');
            $totalValue = $transactionData->sum('total_value');
            $totalValueWithPPN = $totalValue * 1.11;

            // Format the data for display
            $formattedData = $transactionData->map(function ($item) {
                return [
                    'part_code' => $item->part_code,
                    'part_name' => $item->part_name,
                    'part_type' => $item->part_type,
                    'site_part_name' => $item->site_part_name ?: $item->part_name,
                    'quantity' => $item->quantity,
                    'price' => $item->price ?: 0,
                    'total_value' => $item->total_value,
                    'transaction_date' => Carbon::parse($item->transaction_date)->format('d-m-Y'),
                    'wo_number' => $item->wo_number ?: '-',
                    'transaction_status' => $item->transaction_status,
                    'site_name' => $item->site_name,
                    'site_id' => $item->site_id
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedData,
                'summary' => [
                    'total_items' => $transactionData->count(),
                    'total_quantity' => $totalQuantity,
                    'total_value' => $totalValue,
                    'total_value_with_ppn' => $totalValueWithPPN,
                    'division' => $divisionFilter,
                    'date_range' => [
                        'start' => $startDate,
                        'end' => $endDate
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getDivisionPartsDetail: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat data detail divisi',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the parts information page
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function parts(Request $request)
    {
        // Get all sites
        $allSites = Site::all();

        // Reorder sites to ensure Warehouse (WHO) is first
        $warehouseSite = $allSites->where('site_id', 'WHO')->first();
        $otherSites = $allSites->where('site_id', '!=', 'WHO');

        // Create a new collection with Warehouse first
        $sites = collect([$warehouseSite])->merge($otherSites)->filter();

        // Get date parameters from request
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month');

        // Initialize date variables
        $date = Carbon::now();
        $startDate = null;
        $endDate = null;

        // Determine date range based on parameters
        if ($startDateParam && $endDateParam) {
            // Use provided date range
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');

            // Format month name for display
            $startDateObj = Carbon::parse($startDateParam);
            $endDateObj = Carbon::parse($endDateParam);

            if ($startDateObj->format('Y-m') === $endDateObj->format('Y-m')) {
                // Same month
                $monthName = $startDateObj->locale('id')->translatedFormat('F Y');
            } else {
                // Different months
                $monthName = $startDateObj->locale('id')->translatedFormat('d F Y') . ' - ' .
                    $endDateObj->locale('id')->translatedFormat('d F Y');
            }

            // Set selected month for compatibility
            $selectedMonth = $startDateObj->format('Y-m');
        } elseif ($selectedMonth) {
            // Use selected month
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            $monthName = $date->locale('id')->translatedFormat('F Y');
        } else {
            // Default to current month
            $selectedMonth = $date->format('Y-m');
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            $monthName = $date->locale('id')->translatedFormat('F Y');
        }

        // Get part requisition data
        $requisitions = $this->getAllRequisitions();

        // Get penawaran data
        $penawarans = $this->getAllPenawarans();

        // Get part readiness data for all sites
        $sitePartsData = [];
        foreach ($sites as $site) {
            $sitePartsData[$site->site_id] = [
                'site_name' => $site->site_name,
                'address' => $site->address,
                'parts_ready' => $this->getPartsReady($site->site_id),
                'parts_not_ready' => $this->getPartsNotReady($site->site_id)
            ];
        }

        // Get best parts across all sites
        $bestPartsAllSites = $this->getBestPartsAcrossAllSites($startDate, $endDate);

        // Get current year for display
        $currentYear = $date->year;

        return view('superadmin.parts', compact(
            'sites',
            'selectedMonth',
            'monthName',
            'requisitions',
            'penawarans',
            'sitePartsData',
            'bestPartsAllSites',
            'currentYear',
            'startDateParam',
            'endDateParam'
        ));
    }

    /**
     * Get parts data for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPartsData(Request $request)
    {
        // Get search parameter
        $search = $request->input('search', '');

        // Get date parameters from request
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month');

        // Initialize date variables
        $date = Carbon::now();
        $startDate = null;
        $endDate = null;

        // Determine date range based on parameters
        if ($startDateParam && $endDateParam) {
            // Use provided date range
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');
        } elseif ($selectedMonth) {
            // Use selected month
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        } else {
            // Default to current month
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        }

        // Get all sites
        $allSites = Site::all();

        // Reorder sites to ensure Warehouse (WHO) is first
        $warehouseSite = $allSites->where('site_id', 'WHO')->first();
        $otherSites = $allSites->where('site_id', '!=', 'WHO');

        // Create a new collection with Warehouse first
        $sites = collect([$warehouseSite])->merge($otherSites)->filter();

        // Get part requisition data
        $requisitions = $this->getAllRequisitions();

        // Get penawaran data
        $penawarans = $this->getAllPenawarans();

        // Get part readiness data for all sites
        $sitePartsData = [];
        foreach ($sites as $site) {
            $sitePartsData[$site->site_id] = [
                'site_name' => $site->site_name,
                'parts_ready' => $this->getPartsReady($site->site_id, $search),
                'parts_not_ready' => $this->getPartsNotReady($site->site_id, $search)
            ];
        }

        // Get best parts across all sites with the date range
        $bestPartsAllSites = $this->getBestPartsAcrossAllSites($startDate, $endDate);

        return response()->json([
            'requisitions' => $requisitions,
            'penawarans' => $penawarans,
            'site_parts_data' => $sitePartsData,
            'best_parts_all_sites' => $bestPartsAllSites
        ]);
    }
    /**
     * Get parts data for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPartsDataprioritas(Request $request)
    {
        // Get search parameter
        $siteName = $request->input('siteidselect', '');
        $limit = $request->input('jumlahentry', 10);

        // Query parts with priority inventories
        $query = PartInventory::with([
            'part:part_name,part_code'
        ])->select('part_inventory_id', 'part_code', 'priority', 'min_stock', 'max_stock', 'stock_quantity', 'date_priority', 'site_id')
            ->where('priority', 1);

        // Apply filter site jika ada
        if (!empty($siteName)) {
            $query->where('site_id', $siteName);
        }

        // Apply limit jika bukan "1000"
        if (!empty($limit)) {
            $query->limit((int) $limit);
        }

        $datapartprioritas = $query->get();
        return response()->json([
            'datapartprioritas' => $datapartprioritas,
        ]);
    }


    /**
     * Get all penawarans with their items and calculate totals
     *
     * @return array
     */
    protected function getAllPenawarans()
    {
        // Get all penawarans with their items
        $penawarans = Penawaran::with(['items.partInventory.part'])
            ->orderBy('created_at', 'desc')
            ->limit(15)
            ->whereNotIn('status', ['Draft', 'Selesai'])
            ->get();

        $formattedPenawarans = [];

        foreach ($penawarans as $penawaran) {
            $total = 0;
            $itemCount = 0;

            // Calculate total and count items
            foreach ($penawaran->items as $item) {
                $total += $item->quantity * $item->price;
                $itemCount++;
            }

            // Format date
            $createdDate = Carbon::parse($penawaran->created_at);
            $tanggalPenawaran = $penawaran->tanggal_penawaran ? Carbon::parse($penawaran->tanggal_penawaran)->format('d/m/Y') : $createdDate->format('d/m/Y');

            // Get status counts
            $statusCounts = [
                'Ready' => 0,
                'In Order' => 0,
                'Not Ready' => 0
            ];

            foreach ($penawaran->items as $item) {
                if (isset($statusCounts[$item->status])) {
                    $statusCounts[$item->status]++;
                }
            }

            // Calculate readiness percentage
            $readyPercentage = $itemCount > 0 ? round(($statusCounts['Ready'] / $itemCount) * 100) : 0;

            $formattedPenawarans[] = [
                'id' => $penawaran->id,
                'nomor' => $penawaran->nomor,
                'perihal' => $penawaran->perihal,
                'customer' => $penawaran->customer,
                'tanggal' => $tanggalPenawaran,
                'status' => $penawaran->status,
                'total' => $total,
                'item_count' => $itemCount,
                'ready_count' => $statusCounts['Ready'],
                'in_order_count' => $statusCounts['In Order'],
                'not_ready_count' => $statusCounts['Not Ready'],
                'ready_percentage' => $readyPercentage
            ];
        }

        return $formattedPenawarans;
    }

    /**
     * Get penawaran detail by ID
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPenawaranDetail($id)
    {
        try {
            // Find the penawaran with its items and related data
            $penawaran = Penawaran::with(['items.partInventory.part'])
                ->findOrFail($id);

            // Convert to array first to prevent Carbon object serialization issues
            $penawaranArray = $penawaran->toArray();

            // Get raw date values from database without timezone conversion to prevent -1 day offset
            try {
                // Query the database directly to get raw date strings without Carbon casting
                $rawPenawaran = DB::table('penawarans')->where('id', $penawaran->id)->first(['tanggal_penawaran']);

                if ($rawPenawaran && $rawPenawaran->tanggal_penawaran) {
                    // Extract just the date part (YYYY-MM-DD) from the raw database value
                    $penawaranArray['tanggal_penawaran'] = substr($rawPenawaran->tanggal_penawaran, 0, 10);
                }
            } catch (\Exception $dateException) {
                Log::error('Error getting raw dates for penawaran in superadmin: ' . $dateException->getMessage());
                // Fallback to Carbon formatting if raw values are not available
                if ($penawaran->tanggal_penawaran) {
                    $penawaranArray['tanggal_penawaran'] = $penawaran->tanggal_penawaran->format('Y-m-d');
                }
            }

            // Return the penawaran data as JSON
            return response()->json($penawaranArray);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Penawaran tidak ditemukan'], 404);
        }
    }

    /**
     * Get requisitions data for AJAX request with filtering and pagination
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRequisitionsData(Request $request)
    {
        try {
            // Get filter parameters
            $siteFilter = $request->input('site_filter', '');
            $statusFilter = $request->input('status_filter', '');
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 10);

            // Build query
            $query = Requisition::with(['site', 'requisitionDetails.part']);

            // Apply site filter
            if (!empty($siteFilter) && $siteFilter !== 'all_without_who') {
                $query->where('site_id', $siteFilter);
            } elseif ($siteFilter === 'all_without_who') {
                $query->where('site_id', '!=', 'WHO');
            }

            // Apply status filter
            if (!empty($statusFilter) && $statusFilter !== 'all') {
                $query->where('status', $statusFilter);
            }

            // Order by created_at desc
            $query->orderBy('created_at', 'desc');

            // Get total count for pagination
            $total = $query->count();

            // Apply pagination
            $requisitions = $query->skip(($page - 1) * $perPage)
                ->take($perPage)
                ->get()
                ->map(function ($requisition) {
                    // Calculate age in days
                    $createdDate = Carbon::parse($requisition->created_at);
                    $now = Carbon::now();

                    // If created today, show as "Hari ini"
                    if ($createdDate->isToday()) {
                        $ageInDays = 0;
                    } else {
                        // Use diffInDays to get whole days
                        $ageInDays = (int) $createdDate->diffInDays($now);
                    }

                    return [
                        'id' => $requisition->requisition_id,
                        'title' => $requisition->title,
                        'site_name' => $requisition->site->site_name,
                        'site_id' => $requisition->site_id,
                        'status' => $requisition->status,
                        'created_at' => $requisition->created_at->format('d-m-Y'),
                        'age_days' => $ageInDays,
                        'details_count' => $requisition->requisitionDetails->count(),
                        'notes' => $requisition->notes
                    ];
                });

            // Calculate pagination info
            $totalPages = ceil($total / $perPage);
            $hasNextPage = $page < $totalPages;
            $hasPrevPage = $page > 1;

            return response()->json([
                'success' => true,
                'data' => $requisitions->toArray(),
                'pagination' => [
                    'current_page' => (int) $page,
                    'per_page' => (int) $perPage,
                    'total' => $total,
                    'total_pages' => $totalPages,
                    'has_next_page' => $hasNextPage,
                    'has_prev_page' => $hasPrevPage
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error in getRequisitionsData: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat data pengajuan'
            ], 500);
        }
    }

    /**
     * Get all requisitions with status and age information
     *
     * @return array
     */
    private function getAllRequisitions()
    {
        try {
            $requisitions = Requisition::with(['site', 'requisitionDetails.part'])
                ->whereIn('status', ['diajukan', 'pending'])
                ->orderBy('created_at', 'desc')
                ->paginate(10)
                ->map(function ($requisition) {
                    // Calculate age in days
                    $createdDate = Carbon::parse($requisition->created_at);
                    $now = Carbon::now();

                    // If created today, show as "Hari ini"
                    if ($createdDate->isToday()) {
                        $ageInDays = 0;
                    } else {
                        // Use diffInDays to get whole days
                        $ageInDays = (int) $createdDate->diffInDays($now);
                    }

                    return [
                        'id' => $requisition->requisition_id,
                        'title' => $requisition->title,
                        'site_name' => $requisition->site->site_name,
                        'status' => $requisition->status,
                        'created_at' => $requisition->created_at->format('d-m-Y'),
                        'age_days' => $ageInDays,
                        'details_count' => $requisition->requisitionDetails->count(),
                        'notes' => $requisition->notes
                    ];
                });

            return $requisitions->toArray();
        } catch (\Exception $e) {
            Log::error('Error in getAllRequisitions: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get parts that are ready (stock >= max_stock)
     *
     * @param  string  $siteId
     * @param  string  $search
     * @return array
     */
    private function getPartsReady($siteId, $search = '')
    {
        // Base query
        $query = DB::table('part_inventories')
            ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
            ->where('part_inventories.site_id', $siteId);

        // Apply search filter if provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('parts.part_name', 'like', "%{$search}%")
                    ->orWhere('parts.part_code', 'like', "%{$search}%")
                    ->orWhere('part_inventories.site_part_name', 'like', "%{$search}%");
            });
        }

        // Apply new rules for ready parts (aligned with warehouse dashboard):
        // 1. Stock > max_stock (not >=, to match warehouse logic)
        // 2. Ignore parts where both min_stock and max_stock are 0 or null
        $query->whereRaw('part_inventories.stock_quantity >= part_inventories.max_stock')
            ->where(function ($query) {
                $query->where('part_inventories.min_stock', '>', 0);
                $query->where('part_inventories.max_stock', '>', 0);
            }); // Only include parts with either min_stock or max_stock > 0

        $parts = $query->select(
            'parts.part_code',
            'parts.part_name',
            'part_inventories.stock_quantity as stock',
            'part_inventories.min_stock',
            'part_inventories.priority',
            'part_inventories.max_stock'
        )->get();

        return [
            'count' => $parts->count(),
            'items' => $parts
        ];
    }

    /**
     * Get allMonthly Reportdata for the selected period
     *
     * @param  \Carbon\Carbon|null  $date
     * @param  string|null  $siteFilter
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return array
     */
    private function getAllJasaKaryawanData($date = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        try {
            // Set date range for filtering if not provided
            if (!$startDate || !$endDate) {
                if ($date) {
                    $startDate = $date->copy()->startOfMonth()->format('Y-m-d');
                    $endDate = $date->copy()->endOfMonth()->format('Y-m-d');
                }
            } else {
                // Convert datetime format to date format if needed
                if (strpos($startDate, ' ') !== false) {
                    $startDate = substr($startDate, 0, 10);
                }
                if (strpos($endDate, ' ') !== false) {
                    $endDate = substr($endDate, 0, 10);
                }
            }

            // Get allMonthly Reportfor the selected month
            $query = JasaKaryawan::with(['site', 'employee'])
                ->orderBy('date', 'desc');

            // Apply date filter if provided
            if ($startDate && $endDate) {
                $query->whereBetween('date', [$startDate, $endDate]);
            }

            // Apply site filter if provided
            if ($siteFilter) {
                $query->where('site_id', $siteFilter);
            }

            $jasaKaryawan = $query->get();

            // Calculate total amount
            $totalAmount = $jasaKaryawan->sum('amount');

            // Calculate total amount with PPN
            $totalAmountWithPPN = $totalAmount + ($totalAmount * 0.11);

            return [
                'items' => $jasaKaryawan,
                'count' => $jasaKaryawan->count(),
                'total_amount' => $totalAmount,
                'total_amount_with_ppn' => $totalAmountWithPPN
            ];
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in getAllJasaKaryawanData: ' . $e->getMessage());

            // Return empty data
            return [
                'items' => collect([]),
                'count' => 0,
                'total_amount' => 0,
                'total_amount_with_ppn' => 0
            ];
        }
    }

    /**
     * GetMonthly Reportdata for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getJasaKaryawanData(Request $request)
    {
        // Get date range parameters
        $startDateParam = $request->input('start_date');
        $endDateParam = $request->input('end_date');
        $selectedMonth = $request->input('month', Carbon::now()->format('Y-m'));

        // Initialize date variables
        $date = null;
        $startDate = null;
        $endDate = null;

        // Handle date range parameters if provided
        if ($startDateParam && $endDateParam) {
            // Parse date range parameters
            $startDate = Carbon::parse($startDateParam)->startOfDay()->format('Y-m-d H:i:s');
            $endDate = Carbon::parse($endDateParam)->endOfDay()->format('Y-m-d H:i:s');

            // Use the middle date for month-based functions
            $middleDate = Carbon::parse($startDateParam)->addDays(
                Carbon::parse($endDateParam)->diffInDays(Carbon::parse($startDateParam)) / 2
            );
            $date = $middleDate;
        } else {
            // Fallback to month parameter
            $date = Carbon::createFromFormat('Y-m', $selectedMonth);
            $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
            $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
        }

        // Get allMonthly Reportdata for the selected period
        $jasaKaryawanData = $this->getAllJasaKaryawanData($date, null, $startDate, $endDate);

        return response()->json($jasaKaryawanData);
    }

    /**
     * Display the price list page
     *
     * @return \Illuminate\View\View
     */
    public function priceList()
    {
        // Get all sites
        $allSites = Site::all();

        // Reorder sites to ensure Warehouse (WHO) is first
        $warehouseSite = $allSites->where('site_id', 'WHO')->first();
        $otherSites = $allSites->where('site_id', '!=', 'WHO');

        // Create a new collection with Warehouse first
        $sites = collect([$warehouseSite])->merge($otherSites)->filter();
        return view('superadmin.price-list', compact('sites'));
    }

    /**
     * Get price list data for AJAX request
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPriceListData(Request $request)
    {
        // Get parameters from request
        $siteId = $request->input('site_id', 'WHO');
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 5);
        $partType = $request->input('part_type', 'all');

        // Define excluded part types
        $excludedPartTypes = ['PERSEDIAAN LAINNYA', 'PERLENGKAPAN AC'];

        // Different query logic based on site
        if ($siteId === 'WHO') {
            // For Warehouse, show all parts from the parts table
            $query = Part::query()
                ->select('parts.part_code', 'parts.part_name', 'parts.part_type', 'parts.price as base_price')
                ->whereNotIn('parts.part_type', $excludedPartTypes)
                ->with([
                    'partInventories' => function ($query) {
                        $query->select('part_inventories.part_inventory_id', 'part_inventories.part_code', 'part_inventories.site_id', 'part_inventories.price', 'part_inventories.site_part_name')
                            ->with('site:site_id,site_name');
                    }
                ]);

            // Apply search filter if provided
            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->where('parts.part_name', 'like', "%{$search}%")
                        ->orWhere('parts.part_code', 'like', "%{$search}%");
                });
            }

            // Apply part type filter if provided
            if ($partType !== 'all') {
                $query->where('parts.part_type', $partType);
            }
        } else {
            // For other sites, only show parts that exist in that site's inventory
            $query = Part::query()
                ->select('parts.part_code', 'parts.part_name', 'parts.part_type', 'parts.price as base_price')
                ->join('part_inventories', 'parts.part_code', '=', 'part_inventories.part_code')
                ->where('part_inventories.site_id', $siteId)
                ->whereNotIn('parts.part_type', $excludedPartTypes)
                ->with([
                    'partInventories' => function ($query) use ($siteId) {
                        $query->select('part_inventories.part_inventory_id', 'part_inventories.part_code', 'part_inventories.site_id', 'part_inventories.price', 'part_inventories.site_part_name')
                            ->where('part_inventories.site_id', $siteId)
                            ->with('site:site_id,site_name');
                    }
                ]);

            // Apply search filter if provided
            if (!empty($search)) {
                $query->where(function ($q) use ($search) {
                    $q->where('parts.part_name', 'like', "%{$search}%")
                        ->orWhere('parts.part_code', 'like', "%{$search}%")
                        ->orWhere('part_inventories.site_part_name', 'like', "%{$search}%");
                });
            }

            // Apply part type filter if provided
            if ($partType !== 'all') {
                $query->where('parts.part_type', $partType);
            }
        }

        // abaikan setiap part dengan awal jasa
        $query->whereRaw('LOWER(parts.part_name) NOT LIKE ?', ['%jasa%'])
            ->whereRaw('LOWER(parts.part_code) NOT LIKE ?', ['%jasa%']);

        // Get total count before pagination
        $total = $query->count();

        // Apply pagination
        $parts = $query->orderBy('parts.part_name')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        // Get all part types for filtering, excluding specific types
        $partTypes = Part::distinct()
            ->whereNotIn('part_type', $excludedPartTypes)
            ->pluck('part_type')
            ->filter()
            ->values();

        // Format the response
        return response()->json([
            'parts' => $parts,
            'part_types' => $partTypes,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }

    /**
     * Display the superadmin invoices page
     *
     * @return \Illuminate\View\View
     */
    public function invoices()
    {
        return view('superadmin.invoices');
    }

    /**
     * Get invoices data for the superadmin
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInvoicesData(Request $request)
    {
        // Get filter parameters
        $search = $request->input('search', '');
        $status = $request->input('status', '');
        $siteId = $request->input('site_id', '');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 10);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Build query with all necessary relationships
        $query = Invoice::with(['unitTransactions.site', 'penawaran.items', 'site']);

        // Apply search filter if provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('no_invoice', 'like', "%{$search}%")
                    ->orWhere('customer', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if (!empty($status)) {
            if ($status === 'Jatuh Tempo') {
                // For 'Jatuh Tempo', we need to check due dates
                $query->where(function ($q) {
                    $q->where('payment_status', '!=', 'Lunas')
                        ->where(function ($q2) {
                            $q2->where('due_date', '<', now())
                                ->orWhereRaw('DATE_ADD(tanggal_invoice, INTERVAL 30 DAY) < NOW()');
                        });
                });
            } else {
                $query->where('payment_status', $status);
            }
        }

        // Apply site filter if provided
        if (!empty($siteId)) {
            if ($siteId === 'non-site') {
                // For non-site invoices, get invoices that don't have unit transactions
                $query->whereDoesntHave('unitTransactions');
            } else {
                // For specific site, get invoices that have unit transactions with that site
                $query->whereHas('unitTransactions', function ($q) use ($siteId) {
                    $q->where('site_id', $siteId);
                });
            }
        }

        // Apply date range filter if provided
        if (!empty($startDate) && !empty($endDate)) {
            $query->whereBetween('tanggal_invoice', [$startDate, $endDate]);
        }

        // Get total count before pagination
        $total = $query->count();

        // Apply pagination and ordering
        $invoices = $query->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        // Process invoices to add site information and calculate totals
        $processedInvoices = $invoices->map(function ($invoice) {
            // Get site information from the first unit transaction
            $site = null;
            if ($invoice->unitTransactions->isNotEmpty() && $invoice->unitTransactions->first()->site) {
                $site = [
                    'site_id' => $invoice->unitTransactions->first()->site->site_id,
                    'site_name' => $invoice->unitTransactions->first()->site->site_name
                ];
            }

            // Calculate subtotal, tax, and total
            $subtotal = $invoice->getSubtotalAttribute();
            $taxAmount = $invoice->getTaxAmountAttribute();
            $totalAmount = $invoice->getTotalAmountAttribute();

            return [
                'id' => $invoice->id,
                'no_invoice' => $invoice->no_invoice,
                'site' => $site,
                'customer' => $invoice->customer,
                'location' => $invoice->location,
                'tanggal_invoice' => $invoice->tanggal_invoice ? $invoice->tanggal_invoice->format('Y-m-d') : null,
                'due_date' => $invoice->due_date ? $invoice->due_date->format('Y-m-d') : null,
                'subtotal' => $subtotal,
                'ppn' => $invoice->ppn,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_status' => $invoice->payment_status,
                'invoice_status' => $invoice->invoice_status,
                'signed_document_path' => $invoice->signed_document_path,
                'document_path' => $invoice->document_path,
                'unit_list' => $invoice->unit_list
            ];
        });

        // Get all sites for filtering (exclude warehouse)
        $sites = Site::where('site_id', '!=', 'WHO')->get(['site_id', 'site_name']);

        // Format the response
        return response()->json([
            'invoices' => $processedInvoices,
            'sites' => $sites,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }

    /**
     * Export invoices to PDF
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function exportInvoicesPdf(Request $request)
    {
        // Get filter parameters
        $search = $request->input('search', '');
        $status = $request->input('status', '');
        $siteId = $request->input('site_id', '');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Get filtered invoices data
        $invoicesData = $this->getInvoicesForExport($search, $status, $siteId, $startDate, $endDate);

        $data = [
            'invoices' => $invoicesData,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'site_id' => $siteId,
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'exportDate' => now()->format('d-m-Y H:i:s')
        ];

        $pdf = Pdf::loadView('superadmin.exports.invoices-pdf', $data);
        $pdf->setPaper('a4', 'landscape');

        $filename = 'Invoices_Export_' . date('Y-m-d_H-i-s') . '.pdf';
        return $pdf->download($filename);
    }

    /**
     * Export invoices to Excel
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function exportInvoicesExcel(Request $request)
    {
        try {
            // Get filter parameters
            $search = $request->input('search', '');
            $status = $request->input('status', '');
            $siteId = $request->input('site_id', '');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            // Get filtered invoices data
            $invoicesData = $this->getInvoicesForExport($search, $status, $siteId, $startDate, $endDate);

            // Create new Spreadsheet object
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Set document properties
            $spreadsheet->getProperties()
                ->setCreator('Portal PWB')
                ->setTitle('Invoices Export')
                ->setSubject('Invoices Export')
                ->setDescription('Export of invoices data from Portal PWB');

            // Set headers
            $headers = [
                'A1' => 'No',
                'B1' => 'Invoice Number',
                'C1' => 'Site',
                'D1' => 'Customer',
                'E1' => 'Invoice Date',
                'F1' => 'Invoice Value',
                'G1' => 'Tax Value',
                'H1' => 'After-Tax Total',
                'I1' => 'Status'
            ];

            foreach ($headers as $cell => $value) {
                $sheet->setCellValue($cell, $value);
            }

            // Style headers
            $headerStyle = [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']],
                'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]]
            ];
            $sheet->getStyle('A1:I1')->applyFromArray($headerStyle);

            // Add data
            $row = 2;
            foreach ($invoicesData as $index => $invoice) {
                $sheet->setCellValue('A' . $row, $index + 1);
                $sheet->setCellValue('B' . $row, $invoice['no_invoice'] ?? '-');
                $sheet->setCellValue('C' . $row, $invoice['site_name'] ?? '-');
                $sheet->setCellValue('D' . $row, $invoice['customer'] ?? '-');
                $sheet->setCellValue('E' . $row, $invoice['tanggal_invoice']);
                $sheet->setCellValue('F' . $row, $invoice['invoice_value']);
                $sheet->setCellValue('G' . $row, $invoice['tax_value']);
                $sheet->setCellValue('H' . $row, $invoice['after_tax_total']);
                $sheet->setCellValue('I' . $row, $invoice['invoice_status']);
                $row++;
            }

            // Auto-size columns
            foreach (range('A', 'I') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

            // Set filename and headers
            $filename = 'Invoices_Export_' . date('Y-m-d_H-i-s') . '.xlsx';
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header("Content-Disposition: attachment;filename=\"{$filename}\"");
            header('Cache-Control: max-age=0');

            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            Log::error('Error exporting invoices Excel: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat export Excel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get invoices data for export
     *
     * @param  string  $search
     * @param  string  $status
     * @param  string  $siteId
     * @param  string  $startDate
     * @param  string  $endDate
     * @return array
     */
    private function getInvoicesForExport($search, $status, $siteId, $startDate, $endDate)
    {
        // Build query
        $query = Invoice::with(['unitTransactions.site']);

        // Apply search filter
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('no_invoice', 'like', "%{$search}%")
                    ->orWhere('customer', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if (!empty($status)) {
            $query->where('invoice_status', $status);
        }

        // Apply site filter
        if (!empty($siteId)) {
            $query->whereHas('unitTransactions', function ($q) use ($siteId) {
                $q->where('site_id', $siteId);
            });
        }

        // Apply date filter
        if (!empty($startDate) && !empty($endDate)) {
            $query->whereBetween('tanggal_invoice', [$startDate, $endDate]);
        }

        $invoices = $query->orderBy('tanggal_invoice', 'desc')->get();

        // Process invoices data
        $processedInvoices = [];
        foreach ($invoices as $invoice) {
            // Calculate totals
            // AGAK RUMIT, KARENA FUNCTION CALCULATE SUDAH MENGEMBALIKAN AFTER TAX >_<
            // $invoiceValue = $invoice->unitTransactions->sum('total_amount') ?? 0;
            $afterTaxTotal = $this->calculateInvoiceTotal($invoice, null);
            $invoiceValue = $afterTaxTotal / 1.11; // 11% tax
            $TaxValue = $afterTaxTotal - $invoiceValue;

            // Get site name from first unit transaction
            $siteName = $invoice->unitTransactions->first()->site->site_name ?? '-';

            $processedInvoices[] = [
                'no_invoice' => $invoice->no_invoice,
                'site_name' => $siteName,
                'customer' => $invoice->customer,
                'tanggal_invoice' => \Carbon\Carbon::parse($invoice->tanggal_invoice)->format('d-m-Y'),
                'invoice_value' => $invoiceValue,
                'tax_value' => $TaxValue,
                'after_tax_total' => $afterTaxTotal,
                'invoice_status' => $invoice->invoice_status
            ];
        }

        return $processedInvoices;
    }

    /**
     * Export site priority parts to PDF
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function exportPriorityPartsPdf(Request $request)
    {
        // Get filter parameters
        $siteId = $request->input('siteidselect', '');
        $limit = $request->input('jumlahentry', 10);

        // Get filtered priority parts data
        $priorityPartsData = $this->getPriorityPartsForExport($siteId, $limit);

        $data = [
            'priorityParts' => $priorityPartsData,
            'filters' => [
                'site_id' => $siteId,
                'limit' => $limit
            ],
            'exportDate' => now()->format('d-m-Y H:i:s')
        ];

        $pdf = Pdf::loadView('superadmin.exports.priority-parts-pdf', $data);
        $pdf->setPaper('a4', 'landscape');

        $filename = 'Priority_Parts_Export_' . date('Y-m-d_H-i-s') . '.pdf';
        return $pdf->download($filename);
    }

    /**
     * Export site priority parts to Excel
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function exportPriorityPartsExcel(Request $request)
    {
        try {
            // Get filter parameters
            $siteId = $request->input('siteidselect', '');
            $limit = $request->input('jumlahentry', 10);

            // Get filtered priority parts data
            $priorityPartsData = $this->getPriorityPartsForExport($siteId, $limit);

            // Create new Spreadsheet object
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Set document properties
            $spreadsheet->getProperties()
                ->setCreator('Portal PWB')
                ->setTitle('Priority Parts Export')
                ->setSubject('Priority Parts Export')
                ->setDescription('Export of priority parts data from Portal PWB');

            // Set headers
            $headers = [
                'A1' => 'Site',
                'B1' => 'Part Name',
                'C1' => 'Part Code',
                'D1' => 'Min',
                'E1' => 'Max',
                'F1' => 'Stock',
                'G1' => 'Date Marked as Priority'
            ];

            foreach ($headers as $cell => $value) {
                $sheet->setCellValue($cell, $value);
            }

            // Style headers
            $headerStyle = [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ];
            $sheet->getStyle('A1:G1')->applyFromArray($headerStyle);

            // Add data
            $row = 2;
            foreach ($priorityPartsData as $part) {
                $sheet->setCellValue('A' . $row, $part['site_id']);
                $sheet->setCellValue('B' . $row, $part['part_name']);
                $sheet->setCellValue('C' . $row, $part['part_code']);
                $sheet->setCellValue('D' . $row, $part['min_stock']);
                $sheet->setCellValue('E' . $row, $part['max_stock']);
                $sheet->setCellValue('F' . $row, $part['stock_quantity']);
                $sheet->setCellValue('G' . $row, $part['date_priority']);
                $row++;
            }

            // Auto-size columns
            foreach (range('A', 'G') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

            // Set filename and headers
            $filename = 'Priority_Parts_Export_' . date('Y-m-d_H-i-s') . '.xlsx';

            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header("Content-Disposition: attachment;filename=\"{$filename}\"");
            header('Cache-Control: max-age=0');

            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            Log::error('Error exporting priority parts Excel: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat export Excel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get priority parts data for export
     *
     * @param  string  $siteId
     * @param  int  $limit
     * @return array
     */
    private function getPriorityPartsForExport($siteId, $limit)
    {
        // Query parts with priority inventories
        $query = PartInventory::with([
            'part:part_name,part_code'
        ])->select('part_inventory_id', 'part_code', 'priority', 'min_stock', 'max_stock', 'stock_quantity', 'date_priority', 'site_id')
            ->where('priority', 1);

        // Apply filter site jika ada
        if (!empty($siteId)) {
            $query->where('site_id', $siteId);
        }

        // Apply limit jika bukan "1000"
        if (!empty($limit) && $limit != 1000) {
            $query->limit((int) $limit);
        }

        $priorityParts = $query->get();

        // Process priority parts data
        $processedParts = [];
        foreach ($priorityParts as $part) {
            $processedParts[] = [
                'site_id' => $part->site_id,
                'part_name' => $part->part->part_name ?? '-',
                'part_code' => $part->part_code,
                'min_stock' => $part->min_stock ?? 0,
                'max_stock' => $part->max_stock ?? 0,
                'stock_quantity' => $part->stock_quantity ?? 0,
                'date_priority' => $part->date_priority ? Carbon::parse($part->date_priority)->format('d-m-Y') : '-'
            ];
        }

        return $processedParts;
    }

    /**
     * Export price list to PDF
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function exportPriceListPdf(Request $request)
    {
        // Get filter parameters
        $siteId = $request->input('site_id', 'WHO');
        $search = $request->input('search', '');
        $partType = $request->input('part_type', 'all');

        // Get filtered price list data
        $priceListData = $this->getPriceListForExport($siteId, $search, $partType);

        $data = [
            'priceList' => $priceListData,
            'filters' => [
                'site_id' => $siteId,
                'search' => $search,
                'part_type' => $partType
            ],
            'exportDate' => now()->format('d-m-Y H:i:s')
        ];

        $pdf = Pdf::loadView('superadmin.exports.price-list-pdf', $data);
        $pdf->setPaper('a4', 'landscape');

        $filename = 'Price_List_Export_' . date('Y-m-d_H-i-s') . '.pdf';
        return $pdf->download($filename);
    }

    /**
     * Export price list to Excel
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function exportPriceListExcel(Request $request)
    {
        try {
            // Get filter parameters
            $siteId = $request->input('site_id', 'WHO');
            $search = $request->input('search', '');
            $partType = $request->input('part_type', 'all');

            // Get filtered price list data
            $priceListData = $this->getPriceListForExport($siteId, $search, $partType);

            // Create new Spreadsheet object
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Set document properties
            $spreadsheet->getProperties()
                ->setCreator('Portal PWB')
                ->setTitle('Price List Export')
                ->setSubject('Price List Export')
                ->setDescription('Export of price list data from Portal PWB');

            // Set headers
            $headers = [
                'A1' => 'Site',
                'B1' => 'Part Code',
                'C1' => 'Part Name',
                'D1' => 'Type',
                'E1' => 'Harga Beli',
                'F1' => 'Harga Jual'
            ];

            foreach ($headers as $cell => $value) {
                $sheet->setCellValue($cell, $value);
            }

            // Style headers
            $headerStyle = [
                'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => '4472C4']],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
            ];
            $sheet->getStyle('A1:F1')->applyFromArray($headerStyle);

            // Add data
            $row = 2;
            foreach ($priceListData as $part) {
                $sheet->setCellValue('A' . $row, $part['site_id']);
                $sheet->setCellValue('B' . $row, $part['part_code']);
                $sheet->setCellValue('C' . $row, $part['part_name']);
                $sheet->setCellValue('D' . $row, $part['part_type']);
                $sheet->setCellValue('E' . $row, $part['purchase_price']);
                $sheet->setCellValue('F' . $row, $part['price']);
                $row++;
            }

            // Auto-size columns
            foreach (range('A', 'F') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

            // Set filename and headers
            $filename = 'Price_List_Export_' . date('Y-m-d_H-i-s') . '.xlsx';

            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header("Content-Disposition: attachment;filename=\"{$filename}\"");
            header('Cache-Control: max-age=0');

            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
            exit;

        } catch (\Exception $e) {
            Log::error('Error exporting price list Excel: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat export Excel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get price list data for export
     *
     * @param  string  $siteId
     * @param  string  $search
     * @param  string  $partType
     * @return array
     */
    private function getPriceListForExport($siteId, $search, $partType)
    {
        // Define excluded part types
        $excludedPartTypes = ['PERSEDIAAN LAINNYA', 'PERLENGKAPAN AC'];

        // Build query for parts with inventory
        $query = PartInventory::with(['part'])
            ->select('part_code', 'price', 'site_id')
            ->where('site_id', $siteId)
            ->whereHas('part', function ($q) use ($excludedPartTypes) {
                $q->whereNotIn('part_type', $excludedPartTypes)
                ->whereRaw('LOWER(part_name) NOT LIKE ?', ['%jasa%'])
                ->whereRaw('LOWER(part_code) NOT LIKE ?', ['%jasa%']);
            });

        // Apply search filter
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('part_code', 'like', "%{$search}%")
                    ->orWhereHas('part', function ($partQuery) use ($search) {
                        $partQuery->where('part_name', 'like', "%{$search}%");
                    });
            });
        }

        // Apply part type filter
        if ($partType !== 'all') {
            $query->whereHas('part', function ($q) use ($partType) {
                $q->where('part_type', $partType);
            });
        }


        $parts = $query->get();

        // Process price list data
        $processedParts = [];
        foreach ($parts as $partInventory) {
            $processedParts[] = [
                'site_id' => $partInventory->site_id,
                'part_code' => $partInventory->part_code,
                'part_name' => $partInventory->part->part_name ?? '-',
                'part_type' => $partInventory->part->part_type ?? '-',
                'purchase_price' => $partInventory->part->purchase_price ?? 0,
                'price' => $partInventory->price ?? 0
            ];
        }

        return $processedParts;
    }

    /**
     * Get invoice details for the superadmin
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInvoiceDetails($id)
    {
        try {
            // Find the invoice with its unit transactions
            $invoice = Invoice::with([
                'unitTransactions.unit',
                'unitTransactions.parts.partInventory.part',
                'unitTransactions.site'
            ])->findOrFail($id);

            // Calculate subtotal, tax, and total
            $subtotal = $invoice->getSubtotalAttribute();
            $taxAmount = $invoice->getTaxAmountAttribute();
            $totalAmount = $invoice->getTotalAmountAttribute();

            // Format the response
            $response = [
                'id' => $invoice->id,
                'no_invoice' => $invoice->no_invoice,
                'customer' => $invoice->customer,
                'location' => $invoice->location,
                'sn' => $invoice->sn,
                'trouble' => $invoice->trouble,
                'lokasi' => $invoice->lokasi,
                'tanggal_invoice' => $invoice->tanggal_invoice ? $invoice->tanggal_invoice->format('Y-m-d') : null,
                'due_date' => $invoice->due_date ? $invoice->due_date->format('Y-m-d') : null,
                'ppn' => $invoice->ppn,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $totalAmount,
                'payment_status' => $invoice->payment_status,
                'payment_date' => $invoice->payment_date ? $invoice->payment_date->format('Y-m-d') : null,
                'payment_notes' => $invoice->payment_notes,
                'notes' => $invoice->notes,
                'signed_document_path' => $invoice->signed_document_path,
                'document_path' => $invoice->document_path,
                'unit_transactions' => $invoice->unitTransactions->map(function ($transaction) {
                    return [
                        'id' => $transaction->id,
                        'unit' => $transaction->unit ? [
                            'id' => $transaction->unit->id,
                            'unit_code' => $transaction->unit->unit_code,
                            'unit_name' => $transaction->unit->unit_name
                        ] : null,
                        'site' => $transaction->site ? [
                            'site_id' => $transaction->site->site_id,
                            'site_name' => $transaction->site->site_name
                        ] : null,
                        'status' => $transaction->status,
                        'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                        'parts' => $transaction->parts->map(function ($part) {
                            return [
                                'id' => $part->id,
                                'part_name' => $part->partInventory && $part->partInventory->part ?
                                    $part->partInventory->part->part_name : 'Unknown Part',
                                'site_part_name' => $part->partInventory ? $part->partInventory->site_part_name : null,
                                'quantity' => $part->quantity,
                                'price' => $part->price,
                                'total' => $part->quantity * $part->price
                            ];
                        })
                    ];
                })
            ];

            return response()->json([
                'success' => true,
                'invoice' => $response
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get invoice details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Ready Invoice data for the superadmin
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReadyInvoiceData(Request $request)
    {
        // Get filter parameters
        $siteId = $request->input('site_id', '');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $divisionFilter = $request->input('division');

        // Build query for unit transactions that have invoices
        $query = UnitTransaction::with(['unit', 'site', 'parts.partInventory.part', 'invoices'])
            ->whereHas('invoices'); // Only get transactions with invoices

        // Apply site filter if provided
        if (!empty($siteId)) {
            $query->where('site_id', $siteId);
        }

        // Apply date filter if provided
        if ($startDate && $endDate) {
            $query->whereBetween('po_date', [$startDate, $endDate]);
        }

        // Apply division filter if provided
        if ($divisionFilter) {
            $query->whereHas('parts.partInventory.part', function ($q) use ($divisionFilter) {
                $q->where('part_type', $divisionFilter);
            });
        }

        $transactions = $query->get();
        $units = [];
        $totalAmount = 0;

        foreach ($transactions as $transaction) {
            $unitAmount = 0;

            // Calculate unit amount
            foreach ($transaction->parts as $part) {
                $price = $part->price ?? $part->partInventory->price;

                // If division filter is applied, only count parts of that type
                if ($divisionFilter) {
                    if ($part->partInventory && $part->partInventory->part && strtolower($part->partInventory->part->part_type) === $divisionFilter) {
                        $unitAmount += $price * $part->quantity;
                    }
                } else {
                    $unitAmount += $price * $part->quantity;
                }
            }

            // Get invoice information
            $invoice = $transaction->invoices->first();
            $invoiceNumber = $invoice ? $invoice->invoice_number : null;
            $invoiceDate = $invoice ? $invoice->tanggal_invoice : null;

            $units[] = [
                'id' => $transaction->id,
                'unit_code' => $transaction->unit->unit_code ?? '-',
                'unit_name' => $transaction->unit->unit_name ?? '-',
                'status' => $transaction->status,
                'invoice_number' => $invoiceNumber,
                'tanggal_invoice' => $invoiceDate ? \Carbon\Carbon::parse($invoiceDate)->format('d-m-Y') : null,
                'total_amount' => $unitAmount
            ];

            $totalAmount += $unitAmount;
        }

        // Get site name
        $siteName = '';
        if ($siteId) {
            $site = \App\Models\Site::where('site_id', $siteId)->first();
            $siteName = $site ? $site->site_name : $siteId;
        }

        return response()->json([
            'units' => $units,
            'total_count' => count($units),
            'total_amount' => $totalAmount,
            'site_name' => $siteName
        ]);
    }


    /**
     * Get Ready Invoice data untuk suatu site
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReadyInvoiceSite(Request $request)
    {
        // Get filter parameters
        $siteId = $request->input('site_id', '');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Build query for unit transactions that have invoices
        $query = UnitTransaction::with(['unit', 'site', 'parts.partInventory.part', 'invoices'])
            ->whereHas('invoices', function ($q) use ($startDate, $endDate) {
                // Filter by invoice_date from invoices table
                if ($startDate && $endDate) {
                    $q->whereBetween('tanggal_invoice', [$startDate, $endDate]);
                }
            });
        // ->whereBetween('po_date', [$startDate, $endDate]);

        // Apply site filter if provided
        if (!empty($siteId)) {
            $query->where('site_id', $siteId);
        }

        $transactions = $query->get();
        $units = [];
        $totalAmount = 0;

        foreach ($transactions as $transaction) {
            $unitAmount = 0;

            // Calculate unit amount
            foreach ($transaction->parts as $part) {
                $price = $part->price ?? $part->partInventory->price;
                $unitAmount += $price * $part->quantity;
            }
            // Get invoice information
            $invoice = $transaction->invoices->first();
            $no_invoice = $invoice ? $invoice->no_invoice : 'tidak ada';
            $invoiceDate = $invoice ? $invoice->tanggal_invoice : '';
            $podate = $transaction->po_date;
            $signed_document_path = $invoice ? $invoice->document_path : '-';
            $idinv = $invoice ? $invoice->id : '0';

            $units[] = [
                'id' => $transaction->id,
                'unit_code' => $transaction->unit->unit_code ?? '-',
                'unit_name' => $transaction->unit->unit_type ?? '-',
                'status' => $transaction->status,
                'no_invoice' => $no_invoice,
                'idinv' => $idinv,
                // 'invoices' => $invoice, // objext
                'signed_document_path' => $signed_document_path,
                'tanggal_invoice' => $invoiceDate ? \Carbon\Carbon::parse($invoiceDate)->format('d-m-Y') : null,
                'po_date' => $podate ? \Carbon\Carbon::parse($podate)->format('d-m-Y') : null,
                'total_amount' => $unitAmount
            ];
            $totalAmount += $unitAmount;
        }

        // Get site name
        $siteName = '';
        if ($siteId) {
            $site = \App\Models\Site::where('site_id', $siteId)->first();
            $siteName = $site ? $site->site_name : $siteId;
        }

        return response()->json([
            'units' => $units,
            'total_count' => count($units),
            'total_amount' => $totalAmount,
            'site_name' => $siteName
        ]);
    }

    /**
     * Get getPendingPOSite data untuk suatu site
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPendingPOSite(Request $request)
    {
        // Get filter parameters
        $siteId = $request->input('site_id', '');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Build query for unit transactions with status filter
        $query = UnitTransaction::with(['unit', 'site', 'parts'])
            ->whereIn('status', ['Pending', 'MR', 'On Process', 'Ready WO']);

        // Filter by site if provided
        if (!empty($siteId)) {
            $query->where('site_id', $siteId);
        }

        // Optional date filter (based on created_at or another field)
        if ($startDate && $endDate) {
            $query->whereBetween('mr_date', [$startDate, $endDate]);
        }

        $transactions = $query->get();
        $units = [];
        $totalAmount = 0;

        foreach ($transactions as $transaction) {
            $unitAmount = 0;
            foreach ($transaction->parts as $part) {
                $price = $part->price ?? $part->partInventory->price;
                $unitAmount += $price * $part->quantity;
            }

            $units[] = [
                'id' => $transaction->id,
                'mr_date' => $transaction->mr_date,
                'unit_code' => $transaction->unit->unit_code ?? '-',
                'unit_type' => $transaction->unit->unit_type ?? '-',
                'status' => $transaction->status,
                'total_amount' => $unitAmount
            ];
            $totalAmount += $unitAmount;
        }

        // Get site name
        $siteName = '';
        if ($siteId) {
            $site = \App\Models\Site::where('site_id', $siteId)->first();
            $siteName = $site ? $site->site_name : $siteId;
        }

        return response()->json([
            'units' => $units,
            'total_count' => count($units),
            'total_amount' => $totalAmount,
            'site_name' => $siteName
        ]);
    }


    /**
     * Get getREADYPOSite data untuk suatu site
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getreadyPOSite(Request $request)
    {
        // Get filter parameters
        $siteId = $request->input('site_id', '');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Build query for unit transactions with status filter
        $query = UnitTransaction::with(['unit', 'site', 'parts']);
        // ->whereIn('status', ['Ready PO', 'selesai', 'Perbaikan','Pending']);

        // Filter by site if provided
        if (!empty($siteId)) {
            $query->where('site_id', $siteId);
        }

        // Optional date filter (based on created_at or another field)
        if ($startDate && $endDate) {
            $query->whereBetween('po_date', [$startDate, $endDate]);
        }

        $transactions = $query->get();
        $units = [];
        $totalAmount = 0;

        foreach ($transactions as $transaction) {
            $unitAmount = 0;
            foreach ($transaction->parts as $part) {
                $price = $part->price ?? $part->partInventory->price;
                $unitAmount += $price * $part->quantity;
            }

            $units[] = [
                'id' => $transaction->id,
                'mr_date' => $transaction->mr_date,
                'unit_code' => $transaction->unit->unit_code ?? '-',
                'unit_type' => $transaction->unit->unit_type ?? '-',
                'status' => $transaction->status,
                'po_date' => $transaction->po_date,
                'total_amount' => $unitAmount
            ];
            $totalAmount += $unitAmount;
        }

        // Get site name
        $siteName = '';
        if ($siteId) {
            $site = \App\Models\Site::where('site_id', $siteId)->first();
            $siteName = $site ? $site->site_name : $siteId;
        }

        return response()->json([
            'units' => $units,
            'total_count' => count($units),
            'total_amount' => $totalAmount,
            'site_name' => $siteName
        ]);
    }


    /**
     * Get Ready PO transactions data for the superadmin
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getReadyPoData(Request $request)
    {
        // Get filter parameters
        $search = $request->input('search', '');
        $siteId = $request->input('site_id', '');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 10);

        // Build query for unit transactions with "Ready PO" status that don't have invoices
        $query = UnitTransaction::with(['unit', 'site', 'parts.partInventory'])
            ->where('status', 'Ready PO')
            ->whereDoesntHave('invoices'); // Only get transactions without invoices

        // Apply site filter if provided
        if (!empty($siteId)) {
            $query->where('site_id', $siteId);
        }

        // Apply search filter if provided
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('po_number', 'like', "%{$search}%")
                    ->orWhere('noireq', 'like', "%{$search}%") // For IMK site MR numbers
                    ->orWhere('customer', 'like', "%{$search}%")
                    ->orWhereHas('unit', function ($unitQuery) use ($search) {
                        $unitQuery->where('unit_code', 'like', "%{$search}%")
                            ->orWhere('unit_type', 'like', "%{$search}%");
                    });
            });
        }

        // Get total count before pagination
        $total = $query->count();

        // Apply pagination and ordering
        $transactions = $query->orderBy('created_at', 'desc')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        // Process transactions data
        $processedTransactions = $transactions->map(function ($transaction) {
            return [
                'id' => $transaction->id,
                'po_number' => $transaction->po_number,
                'noireq' => $transaction->noireq, // For IMK site MR numbers
                'customer' => $transaction->customer,
                'site' => $transaction->site ? [
                    'site_id' => $transaction->site->site_id,
                    'site_name' => $transaction->site->site_name
                ] : null,
                'unit' => $transaction->unit ? [
                    'unit_code' => $transaction->unit->unit_code,
                    'unit_type' => $transaction->unit->unit_type
                ] : null,
                'parts' => $transaction->parts->map(function ($part) {
                    return [
                        'price' => $part->price ?? $part->partInventory->price,
                        'quantity' => $part->quantity
                    ];
                }),
                'attachment_path' => $transaction->attachment_path,
                'updated_at' => $transaction->updated_at ? $transaction->updated_at->format('Y-m-d H:i:s') : null
            ];
        });

        // Format the response
        return response()->json([
            'transactions' => $processedTransactions,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage)
            ]
        ]);
    }


    // ALL INVOICE INCLUDE PENAWARAN
    public function getPIUTANGInvoices($dateFrom = null, $dateTo = null, $siteid = null, $divisionFilter = null)
    {
        if (empty($dateFrom) || empty($dateTo)) {
            $dateFrom = now()->startOfMonth()->format('Y-m-d');
            $dateTo = now()->format('Y-m-d');
        }

        $query = Invoice::with(['unitTransactions.unit', 'unitTransactions.parts.partInventory.part', 'penawaran.items.partInventory.part'])
            ->whereBetween('tanggal_invoice', [
                $dateFrom . ' 00:00:00',
                $dateTo . ' 23:59:59'
            ])
            ->whereNotIn('payment_status', ['Lunas', 'Draf']);

        if (!empty($siteid)) {
            if ($siteid === 'WHO') {
                $query->where(function ($q) {
                    $q->whereDoesntHave('unitTransactions')
                        ->orWhereHas('unitTransactions', function ($q) {
                            $q->whereNull('site_id')->orWhere('site_id', 'WHO');
                        });
                });
            } else {
                $query->whereHas('unitTransactions', function ($q) use ($siteid) {
                    $q->where('site_id', $siteid);
                });
            }
        }

        $invoicesAll = $query->get();
        $totalprice = 0;

        foreach ($invoicesAll as $invoice) {
            $totalprice += $this->calculateInvoiceTotal($invoice, $divisionFilter);
        }

        return $totalprice;
    }

    // SEMUA INVOICE DENGAN STATUS LUNAS DAN BELUM
    public function gettotalinvoice($dateFrom = null, $dateTo = null, $siteid = null, $divisionFilter = null)
    {
        if (empty($dateFrom) || empty($dateTo)) {
            $dateFrom = now()->startOfMonth()->format('Y-m-d');
            $dateTo = now()->format('Y-m-d');
        }

        $query = Invoice::with(['unitTransactions.unit', 'unitTransactions.parts.partInventory.part', 'penawaran.items.partInventory.part'])
            ->whereBetween('tanggal_invoice', [
                $dateFrom . ' 00:00:00',
                $dateTo . ' 23:59:59'
            ])
            ->whereIn('payment_status', ['Lunas', 'Belum Lunas']);

        if (!empty($siteid)) {
            if ($siteid === 'WHO') {
                $query->where(function ($q) {
                    $q->whereDoesntHave('unitTransactions')
                        ->orWhereHas('unitTransactions', function ($q) {
                            $q->whereNull('site_id')->orWhere('site_id', 'WHO');
                        });
                });
            } else {
                $query->whereHas('unitTransactions', function ($q) use ($siteid) {
                    $q->where('site_id', $siteid);
                });
            }
        }

        $invoicesAll = $query->get();
        $totalprice = 0;

        foreach ($invoicesAll as $invoice) {
            $totalprice += $this->calculateInvoiceTotal($invoice, $divisionFilter);
        }

        return $totalprice;
    }

    // get all invoice
    public function getTotalInvoiceALL($date = null, $divisionFilter = null, $siteFilter = null, $startDate = null, $endDate = null)
    {
        if (!$startDate || !$endDate) {
            if ($date) {
                $startDate = $date->copy()->startOfMonth()->format('Y-m-d H:i:s');
                $endDate = $date->copy()->endOfMonth()->format('Y-m-d H:i:s');
            }
        }

        // Build initial query with date range
        $query = Invoice::with(['unitTransactions.unit', 'unitTransactions.parts.partInventory.part', 'penawaran.items.partInventory.part'])
            ->whereBetween('tanggal_invoice', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ])
            ->whereNotIn('payment_status', ['Belum Lunas', 'Draf']);

        // Apply site filter if provided
        if (!empty($siteFilter)) {
            if ($siteFilter === 'WHO') {
                // For WHO, include invoices without site or explicitly assigned to WHO
                $query->where(function ($q) {
                    $q->whereDoesntHave('unitTransactions')
                        ->orWhereHas('unitTransactions', function ($q) {
                            $q->whereNull('site_id')->orWhere('site_id', 'WHO');
                        });
                });
            } else {
                $query->whereHas('unitTransactions', function ($q) use ($siteFilter) {
                    $q->where('site_id', $siteFilter);
                });
            }
        }

        // Get all invoices
        $invoicesAll = $query->get();
        $totalprice = 0;

        foreach ($invoicesAll as $invoice) {
            $totalprice += $this->calculateInvoiceTotal($invoice, $divisionFilter);
        }
        return $totalprice;
    }



    // Pisahkan logika calculate
    public function calculateInvoiceTotal($invoice, $divisionFilter = null)
    {
        $subtotal = 0;

        // Jika invoice dari penawaran
        if ($invoice->penawaran_id && $invoice->penawaran && $invoice->penawaran->items) {
            foreach ($invoice->penawaran->items as $item) {
                if (!$item->partInventory || !$item->quantity)
                    continue;

                // Apply division filter if specified
                if ($divisionFilter) {
                    if (
                        !$item->partInventory->part ||
                        strtolower($item->partInventory->part->part_type) !== strtolower($divisionFilter)
                    ) {
                        continue;
                    }
                }

                $subtotal += $item->price * $item->quantity;
            }
        }
        // Jika invoice manual
        else {
            $manualParts = \App\Models\ManualInvoicePart::with('part')
                ->where('invoice_id', $invoice->id)
                ->get();

            if ($manualParts->isNotEmpty()) {
                foreach ($manualParts as $item) {
                    if (!$item->part)
                        continue;

                    // Apply division filter if specified
                    if ($divisionFilter) {
                        if (strtolower($item->part->part_type) !== strtolower($divisionFilter)) {
                            continue;
                        }
                    }

                    $subtotal += $item->price * $item->quantity;
                }
            }
            // Hitung dari unitTransactions
            else {
                foreach ($invoice->unitTransactions as $transaction) {
                    if (!$transaction->parts)
                        continue;
                    foreach ($transaction->parts as $part) {
                        if (!$part->partInventory || !$part->quantity)
                            continue;

                        // Apply division filter if specified
                        if ($divisionFilter) {
                            if (
                                !$part->partInventory->part ||
                                strtolower($part->partInventory->part->part_type) !== strtolower($divisionFilter)
                            ) {
                                continue;
                            }
                        }
                        $price = $part->price ?? $part->partInventory->price;
                        $subtotal += $price * $part->quantity;
                    }
                }
            }
        }

        $ppn = $invoice->ppn ?? 0.11;
        return $subtotal + ($subtotal * $ppn);
    }
}