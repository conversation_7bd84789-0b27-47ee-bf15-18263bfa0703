<?php

namespace App\Http\Controllers;

use App\Models\Backlog;
use App\Models\BacklogPart;
use App\Models\Unit;
use App\Models\Part;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
class BacklogController extends Controller
{
    /**
     * Display a listing of the backlogs.
     */
    public function index(Request $request)
    {
        return view('backlogs.index');
    }

    /**
     * Get backlog data for AJAX requests
     */
    public function getData(Request $request)
    {
        $query = Backlog::with('unit');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('unit_code')) {
            $query->where('unit_code', 'LIKE', '%' . $request->unit_code . '%');
        }

        if ($request->filled('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        $backlogs = $query->orderBy('created_at', 'desc')->paginate(10);

        // Format data for frontend
        $formattedBacklogs = $backlogs->map(function ($backlog) {
            return [
                'id' => $backlog->id,
                'unit_code' => $backlog->unit_code,
                'unit_type' => $backlog->unit->unit_type ?? null,
                'hm_found' => $backlog->hm_found,
                'problem_description' => $backlog->problem_description,
                'backlog_job' => $backlog->backlog_job,
                'plan_hm' => $backlog->plan_hm,
                'status' => $backlog->status,
                'status_badge_class' => $this->getStatusBadgeClass($backlog->status),
                'plan_pull_date' => $backlog->plan_pull_date,
                'notes' => $backlog->notes,
                'formatted_created_date' => $backlog->created_at->format('d/m/Y'),
                'formatted_plan_pull_date' => $backlog->plan_pull_date ? $backlog->plan_pull_date->format('d/m/Y H:i') : null,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedBacklogs,
            'current_page' => $backlogs->currentPage(),
            'last_page' => $backlogs->lastPage(),
            'per_page' => $backlogs->perPage(),
            'total' => $backlogs->total()
        ]);
    }

    /**
     * Get status badge class
     */
    private function getStatusBadgeClass($status)
    {
        switch ($status) {
            case 'OPEN':
                return 'bg-warning text-dark';
            case 'CLOSED':
                return 'bg-success';
            default:
                return 'bg-secondary';
        }
    }

    /**
     * Show the form for creating a new backlog.
     */
    public function create()
    {
        return view('backlogs.create');
    }

    /**
     * Store a newly created backlog in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'unit_code' => 'required|string|exists:units,unit_code',
            'hm_found' => 'nullable|numeric|min:0',
            'problem_description' => 'required|string|max:255',
            'backlog_job' => 'required|string|max:255',
            'plan_hm' => 'nullable|numeric|min:0',
            'status' => 'required|in:OPEN,CLOSED',
            'plan_pull_date' => 'nullable|date',
            'notes' => 'nullable|string',
            'parts' => 'required|array|min:1',
            'parts.*.part_code' => 'required|string|exists:parts,part_code',
            'parts.*.quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $backlog = Backlog::create($request->only([
                'unit_code', 'hm_found', 'problem_description', 'backlog_job',
                'plan_hm', 'status', 'plan_pull_date', 'notes'
            ]));

            // Add parts to backlog
            foreach ($request->parts as $partData) {
                BacklogPart::create([
                    'backlog_id' => $backlog->id,
                    'part_code' => $partData['part_code'],
                    'quantity' => $partData['quantity'],
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Backlog berhasil ditambahkan',
                'data' => $backlog->load('backlogParts.part')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menyimpan data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified backlog.
     */
    public function show(Backlog $backlog)
    {
        $backlog->load(['unit', 'backlogParts.part']);

        // Format data for AJAX response
        $formattedBacklog = [
            'id' => $backlog->id,
            'unit_code' => $backlog->unit_code,
            'unit_type' => $backlog->unit->unit_type ?? null,
            'hm_found' => $backlog->hm_found,
            'problem_description' => $backlog->problem_description,
            'backlog_job' => $backlog->backlog_job,
            'plan_hm' => $backlog->plan_hm,
            'status' => $backlog->status,
            'plan_pull_date' => $backlog->plan_pull_date,
            'notes' => $backlog->notes,
            'parts' => $backlog->backlogParts->map(function ($backlogPart) {
                return [
                    'part_code' => $backlogPart->part_code,
                    'part_name' => $backlogPart->part->part_name ?? '',
                    'quantity' => $backlogPart->quantity,
                ];
            }),
        ];

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => $formattedBacklog
            ]);
        }

        return view('backlogs.show', compact('backlog'));
    }

    /**
     * Show the form for editing the specified backlog.
     */
    public function edit(Backlog $backlog)
    {
        $backlog->load('unit');
        return view('backlogs.edit', compact('backlog'));
    }

    /**
     * Update the specified backlog in storage.
     */
    public function update(Request $request, Backlog $backlog)
    {
        $validator = Validator::make($request->all(), [
            'unit_code' => 'required|string|exists:units,unit_code',
            'hm_found' => 'nullable|numeric|min:0',
            'problem_description' => 'required|string|max:255',
            'backlog_job' => 'required|string|max:255',
            'plan_hm' => 'nullable|numeric|min:0',
            'status' => 'required|in:OPEN,CLOSED',
            'plan_pull_date' => 'nullable|date',
            'notes' => 'nullable|string',
            'parts' => 'required|array|min:1',
            'parts.*.part_code' => 'required|string|exists:parts,part_code',
            'parts.*.quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $backlog->update($request->only([
                'unit_code', 'hm_found', 'problem_description', 'backlog_job',
                'plan_hm', 'status', 'plan_pull_date', 'notes'
            ]));

            // Delete existing parts
            $backlog->backlogParts()->delete();

            // Add updated parts to backlog
            foreach ($request->parts as $partData) {
                BacklogPart::create([
                    'backlog_id' => $backlog->id,
                    'part_code' => $partData['part_code'],
                    'quantity' => $partData['quantity'],
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Backlog berhasil diperbarui',
                'data' => $backlog->load('backlogParts.part')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified backlog from storage.
     */
    public function destroy(Backlog $backlog)
    {
        try {
            $backlog->delete();

            return response()->json([
                'success' => true,
                'message' => 'Backlog berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus data'
            ], 500);
        }
    }

    /**
     * Search units for autocomplete
     */
    public function searchUnits(Request $request)
    {
        $search = $request->get('search', '');
        
        $units = Unit::select('unit_code', 'unit_type')
            ->where(function ($query) use ($search) {
                $query->where('unit_code', 'LIKE', "%{$search}%")
                      ->orWhere('unit_type', 'LIKE', "%{$search}%");
            })
            ->distinct()
            ->where('site_id', session('site_id'))
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $units
        ]);
    }

    /**
     * Get open backlogs for a specific unit
     */
    public function getUnitBacklogs($unitCode)
    {
        $backlogs = Backlog::with('unit')
            ->where('unit_code', $unitCode)
            ->where('status', 'OPEN')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $backlogs
        ]);
    }

    /**
     * Search parts for autocomplete
     */
    public function searchParts(Request $request)
    {
        $search = $request->get('search', '');

        $parts = Part::select('part_code', 'part_name')
            ->where(function ($query) use ($search) {
                $query->where('part_code', 'LIKE', "%{$search}%")
                      ->orWhere('part_name', 'LIKE', "%{$search}%");
            })
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $parts
        ]);
    }
}
