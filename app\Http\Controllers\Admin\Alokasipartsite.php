<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Part;
use App\Models\Site;
use App\Models\PartInventory;
use App\Models\LogAktivitas;
use Illuminate\Support\Facades\Validator;
use Illuminate\View\View;
class Alokasipartsite extends Controller
{
    public function index(Request $request): View
    {
        $sites = Site::all();
        $selectedSite = $request->input('site_id');
        $search = $request->input('search');
        $partType = $request->input('part_type');
        $page = $request->input('page', 1);
        $perPage = 15; // 15 items per page

        $query = PartInventory::with(['part', 'site']);

        // Filter by site
        $query->when($selectedSite, function ($q) use ($selectedSite) {
            $q->where('site_id', $selectedSite);
        });

        // Filter by part type
        $query->when($partType, function ($q) use ($partType) {
            $q->whereHas('part', function ($subQ) use ($partType) {
                $subQ->where('part_type', $partType);
            });
        });

        // Search by part code or name
        if ($search) {
            $query->whereHas('part', function ($q) use ($search) {
                $q->where('part_code', 'like', "%$search%")
                  ->orWhere('part_name', 'like', "%$search%");
            });
        }

        // Get paginated parts for the first page
        $parts = $query->paginate($perPage);

        // Initial data for JavaScript
        $initialData = [
            'current_page' => $parts->currentPage(),
            'per_page' => $perPage,
            'last_page' => $parts->lastPage(),
            'total' => $parts->total()
        ];

        return view('parts.pengelompokansitepart', compact('sites', 'parts', 'selectedSite', 'search', 'partType', 'initialData'));
    }

    public function autocomplete(Request $request)
    {
        $search = $request->query('search');
        $parts = Part::with(['partInventories' => function($query) {
                $query->where('site_id', session('site_id', 1));
            }])
            ->where(function($query) use ($search) {
                $query->where('part_code', 'LIKE', "%$search%")
                      ->orWhere('part_name', 'LIKE', "%$search%");
            })
            ->limit(10)
            ->get(['part_code', 'part_name', 'eum', 'price'])
            ->map(function($part) {
                $inventory = $part->partInventories->first();
                return [
                    'part_code' => $part->part_code,
                    'part_name' => $part->part_name,
                    'eum' => $part->eum ?? 'EA',
                    'price' => $inventory ? $inventory->price : $part->price,
                    'min_stock' => $inventory ? $inventory->min_stock : 0,
                    'max_stock' => $inventory ? $inventory->max_stock : 0,
                    'oum' => $inventory ? $inventory->oum : ($part->eum ?? 'EA'),
                ];
            });

        return response()->json($parts);
    }

    public function filter(Request $request)
    {
        $selectedSite = $request->input('site_id');
        $search = $request->input('search');
        $partType = $request->input('part_type');
        $page = $request->input('page', 1); // Get page number
        $perPage = $request->input('per_page', 15); // Default to 15 items per page

        $query = PartInventory::with(['part', 'site']);

        // Filter by site
        $query->when($selectedSite, function ($q) use ($selectedSite) {
            $q->where('site_id', $selectedSite);
        });

        // Filter by part type
        $query->when($partType, function ($q) use ($partType) {
            $q->whereHas('part', function ($subQ) use ($partType) {
                $subQ->where('part_type', $partType);
            });
        });

        // Search by part code or name
        if ($search) {
            $query->whereHas('part', function ($q) use ($search) {
                $q->where('part_code', 'like', "%$search%")
                  ->orWhere('part_name', 'like', "%$search%");
            });
        }

        // Use Laravel's built-in pagination
        $parts = $query->paginate($perPage, ['*'], 'page', $page);

        // Return data with pagination metadata
        return response()->json([
            'parts' => $parts->items(),
            'current_page' => $parts->currentPage(),
            'per_page' => $parts->perPage(),
            'last_page' => $parts->lastPage(),
            'total' => $parts->total()
        ]);
    }

    public function store(Request $request)
    {
        try {
            $data = $request->json()->all();

            $validator = Validator::make($data, [
                'part_code' => 'required|exists:parts,part_code',
                'sites' => 'required|array',
                'sites.*.site_id' => 'required|exists:sites,site_id',
                'sites.*.site_part_name' => 'nullable|string|max:255',
                'sites.*.min_stock' => 'required|integer|min:0',
                'sites.*.price' => 'required|integer|min:0',
                'sites.*.max_stock' => 'required|integer|min:0|gte:sites.*.min_stock',
            ]);

            if ($validator->fails()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }

            $partCode = $data['part_code'];

            foreach ($data['sites'] as $siteData) {
                $siteId = $siteData['site_id'];
                $sitePartName = $siteData['site_part_name'] ?? null;
                $minStock = $siteData['min_stock'];
                $price = $siteData['price'];
                $maxStock = $siteData['max_stock'];

                PartInventory::updateOrCreate(
                    ['part_code' => $partCode, 'site_id' => $siteId],
                    [
                        'site_part_name' => $sitePartName,
                        'min_stock' => $minStock,
                        'price' => $price,
                        'max_stock' => $maxStock
                    ]
                );

                // Get part name for logging
                $part = Part::where('part_code', $partCode)->first();
                $site = Site::where('site_id', $siteId)->first();

                // Log the activity
                LogAktivitas::create([
                    'site_id' => session('site_id'),
                    'name' => session('name'),
                    'action' => 'Mengubah Data Part Site',
                    'description' => "User " . session('name') . " mengubah Data Part " . $part->part_name . " di site " . $site->site_name . " min = " . $minStock . " max = " . $maxStock,
                    'table' => "Part Inventory",
                    'ip_address' => $request->ip(),
                ]);
            }

            return response()->json(['message' => 'Data berhasil disimpan']);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Error: ' . $e->getMessage()], 500);
        }
    }

    public function destroy(PartInventory $partInventory)
    {
        try {
            // Check if there are related records in site_in_stocks or other tables
            if ($partInventory->siteInStocks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada transaksi In Stock'
                ], 400);
            }

            if ($partInventory->siteOutStocks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada transaksi Out Stock'
                ], 400);
            }

            if ($partInventory->warehouseInStocks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada transaksi Warehouse In Stock'
                ], 400);
            }

            if ($partInventory->warehouseOutStocks()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada transaksi Warehouse Out Stock'
                ], 400);
            }

            $partInventory->delete();
            return response()->json([
                'success' => true,
                'message' => 'Data berhasil dihapus'
            ]);
        } catch (\PDOException $e) {
            // Check for foreign key constraint violation
            if ($e->getCode() == 23000) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tidak dapat menghapus data karena masih digunakan pada tabel lain'
                ], 400);
            }

            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus data. Silahkan coba beberapa saat lagi.'
            ], 500);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus data. Silahkan coba beberapa saat lagi.'
            ], 500);
        }
    }

    public function getSites(Request $request)
    {
        $sites = Site::all();
        return response()->json($sites);
    }

    /**
     * Update min or max stock for a part inventory
     */
    public function updateStock(Request $request, PartInventory $partInventory)
    {
        $validator = Validator::make($request->all(), [
            'min_stock' => 'sometimes|required|integer|min:0',
            'max_stock' => 'sometimes|required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updateData = [];
            $logMessage = '';

            if ($request->has('min_stock')) {
                $updateData['min_stock'] = $request->min_stock;
                $logMessage = 'mengupdate min stock menjadi ' . $request->min_stock;
            }

            if ($request->has('max_stock')) {
                $updateData['max_stock'] = $request->max_stock;
                $logMessage = 'mengupdate max stock menjadi ' . $request->max_stock;
            }

            // Validate that min_stock is not greater than max_stock
            $currentMinStock = $request->has('min_stock') ? $request->min_stock : $partInventory->min_stock;
            $currentMaxStock = $request->has('max_stock') ? $request->max_stock : $partInventory->max_stock;

            if ($currentMinStock > $currentMaxStock) {
                return response()->json([
                    'success' => false,
                    'message' => 'Min stock tidak boleh lebih besar dari max stock'
                ], 422);
            }

            $partInventory->update($updateData);

            // Log the activity
            LogAktivitas::create([
                'site_id' => session('site_id'),
                'name' => session('name'),
                'action' => 'Update Stock Part Inventory',
                'description' => 'User ' . session('name') . ' ' . $logMessage . ' untuk part ' . $partInventory->part->part_code,
                'table' => 'part_inventories',
                'ip_address' => request()->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Stock berhasil diupdate',
                'data' => $partInventory->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengupdate stock. Silahkan coba beberapa saat lagi.'
            ], 500);
        }
    }
}