<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Penawaran;
use App\Models\PenawaranItem;
use App\Models\Part;
use App\Models\PartInventory;
use App\Models\Site;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PenawaranDuplicatePartsTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $site;
    protected $part;
    protected $partInventory;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test site
        $this->site = Site::factory()->create([
            'site_name' => 'Test Site',
            'site_code' => 'TST'
        ]);

        // Create a test user
        $this->user = User::factory()->create([
            'site_id' => $this->site->site_id,
            'role' => 'sales'
        ]);

        // Create a test part
        $this->part = Part::factory()->create([
            'part_code' => 'TEST-001',
            'part_name' => 'Test Part',
            'part_type' => 'AC',
            'price' => 100000,
            'purchase_price' => 80000,
            'eum' => 'PCS'
        ]);

        // Create part inventory
        $this->partInventory = PartInventory::factory()->create([
            'part_inventory_id' => 1,
            'part_code' => $this->part->part_code,
            'site_id' => $this->site->site_id,
            'stock_quantity' => 100,
            'price' => 100000
        ]);
    }

    /** @test */
    public function it_can_create_penawaran_with_duplicate_parts()
    {
        // Authenticate as sales user
        $this->actingAs($this->user);

        // Prepare request data with duplicate parts
        $requestData = [
            'nomor' => 'PNW-TEST-001',
            'tanggal_penawaran' => now()->format('Y-m-d'),
            'perihal' => 'Test Quotation',
            'customer' => 'Test Customer',
            'customer_code' => 'CUST001',
            'attn' => 'Test Person',
            'lokasi' => 'Test Location',
            'showcode' => '1',
            'notes' => 'Test notes',
            'diskon' => 0,
            'parts' => [
                [
                    'part_inventory_id' => $this->partInventory->part_inventory_id,
                    'nama_part' => 'Test Part - First Instance',
                    'quantity' => 2,
                    'price' => 100000,
                    'status' => 'Not Ready'
                ],
                [
                    'part_inventory_id' => $this->partInventory->part_inventory_id,
                    'nama_part' => 'Test Part - Second Instance',
                    'quantity' => 3,
                    'price' => 95000,
                    'status' => 'Ready'
                ],
                [
                    'part_inventory_id' => $this->partInventory->part_inventory_id,
                    'nama_part' => 'Test Part - Third Instance',
                    'quantity' => 1,
                    'price' => 110000,
                    'status' => 'In Order'
                ]
            ]
        ];

        // Make the request
        $response = $this->postJson('/sales/penawaran', $requestData);

        // Assert successful response
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Penawaran berhasil disimpan'
                ]);

        // Assert penawaran was created
        $this->assertDatabaseHas('penawarans', [
            'nomor' => 'PNW-TEST-001',
            'customer' => 'Test Customer'
        ]);

        // Get the created penawaran
        $penawaran = Penawaran::where('nomor', 'PNW-TEST-001')->first();
        $this->assertNotNull($penawaran);

        // Assert all three duplicate parts were created as separate items
        $this->assertEquals(3, $penawaran->items()->count());

        // Assert each item has the correct data
        $items = $penawaran->items()->orderBy('id')->get();
        
        // First instance
        $this->assertEquals($this->partInventory->part_inventory_id, $items[0]->part_inventory_id);
        $this->assertEquals('Test Part - First Instance', $items[0]->nama_part);
        $this->assertEquals(2, $items[0]->quantity);
        $this->assertEquals(100000, $items[0]->price);
        $this->assertEquals('Not Ready', $items[0]->status);

        // Second instance
        $this->assertEquals($this->partInventory->part_inventory_id, $items[1]->part_inventory_id);
        $this->assertEquals('Test Part - Second Instance', $items[1]->nama_part);
        $this->assertEquals(3, $items[1]->quantity);
        $this->assertEquals(95000, $items[1]->price);
        $this->assertEquals('Ready', $items[1]->status);

        // Third instance
        $this->assertEquals($this->partInventory->part_inventory_id, $items[2]->part_inventory_id);
        $this->assertEquals('Test Part - Third Instance', $items[2]->nama_part);
        $this->assertEquals(1, $items[2]->quantity);
        $this->assertEquals(110000, $items[2]->price);
        $this->assertEquals('In Order', $items[2]->status);
    }

    /** @test */
    public function it_can_update_penawaran_with_duplicate_parts()
    {
        // Authenticate as sales user
        $this->actingAs($this->user);

        // Create initial penawaran
        $penawaran = Penawaran::factory()->create([
            'nomor' => 'PNW-TEST-002',
            'customer' => 'Test Customer 2'
        ]);

        // Add initial item
        PenawaranItem::factory()->create([
            'penawaran_id' => $penawaran->id,
            'part_inventory_id' => $this->partInventory->part_inventory_id,
            'nama_part' => 'Original Part',
            'quantity' => 1,
            'price' => 100000
        ]);

        // Update with duplicate parts
        $updateData = [
            'nomor' => 'PNW-TEST-002',
            'tanggal_penawaran' => now()->format('Y-m-d'),
            'perihal' => 'Updated Test Quotation',
            'customer' => 'Test Customer 2',
            'customer_code' => 'CUST002',
            'attn' => 'Updated Person',
            'lokasi' => 'Updated Location',
            'showcode' => '1',
            'notes' => 'Updated notes',
            'diskon' => 5,
            'parts' => [
                [
                    'part_inventory_id' => $this->partInventory->part_inventory_id,
                    'nama_part' => 'Updated Part - Instance 1',
                    'quantity' => 5,
                    'price' => 90000,
                    'status' => 'Ready'
                ],
                [
                    'part_inventory_id' => $this->partInventory->part_inventory_id,
                    'nama_part' => 'Updated Part - Instance 2',
                    'quantity' => 2,
                    'price' => 105000,
                    'status' => 'Not Ready'
                ]
            ]
        ];

        // Make the update request
        $response = $this->putJson("/sales/penawaran/{$penawaran->id}", $updateData);

        // Assert successful response
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Penawaran berhasil diperbarui'
                ]);

        // Refresh the penawaran
        $penawaran->refresh();

        // Assert penawaran was updated
        $this->assertEquals('Updated Test Quotation', $penawaran->perihal);
        $this->assertEquals(5, $penawaran->diskon);

        // Assert old items were deleted and new duplicate items were created
        $this->assertEquals(2, $penawaran->items()->count());

        // Assert each updated item has the correct data
        $items = $penawaran->items()->orderBy('id')->get();
        
        // First instance
        $this->assertEquals('Updated Part - Instance 1', $items[0]->nama_part);
        $this->assertEquals(5, $items[0]->quantity);
        $this->assertEquals(90000, $items[0]->price);
        $this->assertEquals('Ready', $items[0]->status);

        // Second instance
        $this->assertEquals('Updated Part - Instance 2', $items[1]->nama_part);
        $this->assertEquals(2, $items[1]->quantity);
        $this->assertEquals(105000, $items[1]->price);
        $this->assertEquals('Not Ready', $items[1]->status);
    }

    /** @test */
    public function it_calculates_totals_correctly_with_duplicate_parts()
    {
        // Create penawaran with duplicate parts having different prices
        $penawaran = Penawaran::factory()->create();

        // Create multiple items with same part but different quantities and prices
        PenawaranItem::factory()->create([
            'penawaran_id' => $penawaran->id,
            'part_inventory_id' => $this->partInventory->part_inventory_id,
            'quantity' => 2,
            'price' => 100000 // Total: 200,000
        ]);

        PenawaranItem::factory()->create([
            'penawaran_id' => $penawaran->id,
            'part_inventory_id' => $this->partInventory->part_inventory_id,
            'quantity' => 3,
            'price' => 95000 // Total: 285,000
        ]);

        PenawaranItem::factory()->create([
            'penawaran_id' => $penawaran->id,
            'part_inventory_id' => $this->partInventory->part_inventory_id,
            'quantity' => 1,
            'price' => 110000 // Total: 110,000
        ]);

        // Calculate expected total: 200,000 + 285,000 + 110,000 = 595,000
        $expectedTotal = 595000;

        // Get the penawaran with items
        $penawaranWithItems = Penawaran::with('items')->find($penawaran->id);
        
        // Calculate actual total
        $actualTotal = $penawaranWithItems->items->sum(function ($item) {
            return $item->quantity * $item->price;
        });

        $this->assertEquals($expectedTotal, $actualTotal);
    }
}
