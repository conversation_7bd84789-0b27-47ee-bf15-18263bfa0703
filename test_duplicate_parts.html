<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Duplicate Parts Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .part-item {
            border: 1px solid #ccc;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            background-color: #f9f9f9;
        }
        .part-code {
            font-weight: bold;
            color: #007bff;
        }
        .part-details {
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Penawaran Duplicate Parts Test</h1>
    
    <div class="test-section">
        <h2>Test 1: JavaScript Duplicate Check Removal</h2>
        <p>This test simulates adding the same part multiple times to verify the duplicate check has been removed.</p>
        
        <div id="parts-container">
            <h3>Selected Parts:</h3>
            <div id="parts-list"></div>
        </div>
        
        <button onclick="addSamplePart()">Add Sample Part (TEST-001)</button>
        <button onclick="clearParts()">Clear All Parts</button>
        
        <div id="test1-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Calculation with Duplicate Parts</h2>
        <p>This test verifies that calculations work correctly with multiple instances of the same part.</p>
        
        <div id="calculation-result">
            <h3>Total Calculation:</h3>
            <div id="total-display">Total: Rp 0</div>
        </div>
        
        <button onclick="testCalculations()">Test Calculations</button>
    </div>

    <div class="test-section">
        <h2>Test Results Summary</h2>
        <div id="summary-results">
            <p>Run the tests above to see results here.</p>
        </div>
    </div>

    <script>
        // Simulate the selectedParts array from the actual application
        let selectedParts = [];
        let testResults = [];

        // Simulate the addPartToTable function (modified version without duplicate check)
        function addPartToTable(part) {
            // Allow multiple entries of the same part - no duplicate check
            // Each entry will be treated as a separate line item

            // Add part to selectedParts array
            const newPart = {
                part_inventory_id: part.part_inventory_id,
                part_code: part.part_code,
                part_name: part.part_name || part.site_part_name || '',
                nama_part: part.part_name || part.site_part_name || '',
                quantity: 1,
                price: part.price || 0,
                stock_quantity: part.stock_quantity,
                status: 'Not Ready',
                is_custom: false,
            };

            selectedParts.push(newPart);
            renderPartsTable();
        }

        function renderPartsTable() {
            const partsList = document.getElementById('parts-list');
            partsList.innerHTML = '';
            
            let total = 0;
            
            selectedParts.forEach((part, index) => {
                const partTotal = part.quantity * part.price;
                total += partTotal;
                
                const partDiv = document.createElement('div');
                partDiv.className = 'part-item';
                partDiv.innerHTML = `
                    <div class="part-code">${part.part_code}</div>
                    <div class="part-details">
                        Name: ${part.nama_part}<br>
                        Quantity: ${part.quantity}<br>
                        Price: Rp ${part.price.toLocaleString()}<br>
                        Subtotal: Rp ${partTotal.toLocaleString()}<br>
                        Index: ${index}
                    </div>
                `;
                partsList.appendChild(partDiv);
            });
            
            document.getElementById('total-display').textContent = `Total: Rp ${total.toLocaleString()}`;
        }

        function addSamplePart() {
            const samplePart = {
                part_inventory_id: 1,
                part_code: 'TEST-001',
                part_name: 'Test Part',
                price: 100000,
                stock_quantity: 50
            };
            
            addPartToTable(samplePart);
            
            // Update test results
            const partCount = selectedParts.filter(p => p.part_code === 'TEST-001').length;
            document.getElementById('test1-result').innerHTML = `
                <div class="success">
                    <strong>✓ Success!</strong> Added part TEST-001. 
                    Total instances of TEST-001: ${partCount}
                    <br>No duplicate warning shown - duplicate check successfully removed!
                </div>
            `;
            
            testResults[0] = true;
            updateSummary();
        }

        function clearParts() {
            selectedParts = [];
            renderPartsTable();
            document.getElementById('test1-result').innerHTML = '';
        }

        function testCalculations() {
            // Clear existing parts
            selectedParts = [];
            
            // Add multiple instances of the same part with different quantities and prices
            const testParts = [
                { part_inventory_id: 1, part_code: 'TEST-001', part_name: 'Test Part - Instance 1', price: 100000 },
                { part_inventory_id: 1, part_code: 'TEST-001', part_name: 'Test Part - Instance 2', price: 95000 },
                { part_inventory_id: 1, part_code: 'TEST-001', part_name: 'Test Part - Instance 3', price: 110000 }
            ];
            
            testParts.forEach(part => addPartToTable(part));
            
            // Modify quantities for testing
            selectedParts[0].quantity = 2; // 2 * 100,000 = 200,000
            selectedParts[1].quantity = 3; // 3 * 95,000 = 285,000
            selectedParts[2].quantity = 1; // 1 * 110,000 = 110,000
            // Expected total: 595,000
            
            renderPartsTable();
            
            const expectedTotal = 595000;
            const actualTotal = selectedParts.reduce((sum, part) => sum + (part.quantity * part.price), 0);
            
            const isCorrect = actualTotal === expectedTotal;
            
            document.getElementById('calculation-result').innerHTML = `
                <h3>Calculation Test Results:</h3>
                <div class="${isCorrect ? 'success' : 'error'}">
                    <strong>${isCorrect ? '✓ Success!' : '✗ Failed!'}</strong><br>
                    Expected Total: Rp ${expectedTotal.toLocaleString()}<br>
                    Actual Total: Rp ${actualTotal.toLocaleString()}<br>
                    ${isCorrect ? 'Calculations work correctly with duplicate parts!' : 'Calculation error detected!'}
                </div>
                <div id="total-display">Total: Rp ${actualTotal.toLocaleString()}</div>
            `;
            
            testResults[1] = isCorrect;
            updateSummary();
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summary-results');
            const test1Status = testResults[0] ? '✓ PASSED' : '✗ FAILED';
            const test2Status = testResults[1] ? '✓ PASSED' : '✗ FAILED';
            
            summaryDiv.innerHTML = `
                <h3>Test Results:</h3>
                <p><strong>Test 1 - Duplicate Check Removal:</strong> ${test1Status}</p>
                <p><strong>Test 2 - Calculation with Duplicates:</strong> ${test2Status}</p>
                <p><strong>Overall Status:</strong> ${testResults[0] && testResults[1] ? '✓ ALL TESTS PASSED' : '⚠ SOME TESTS FAILED'}</p>
            `;
        }

        // Initialize
        renderPartsTable();
    </script>
</body>
</html>
