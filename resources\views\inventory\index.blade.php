@extends('warehouse.content')
@section('resource')

@vite(['resources/js/adminho/stockmonitoring.js', 'resources/js/style.js'])
@endsection
@section('contentho')
<style>
    .bg-danger {
        background-color:rgb(217, 134, 140) !important;
    }
    body{
        font-size: 11px;
    }
</style>
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>
</div>

<div class="bgwhite p-3  shadow-kit">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 font-bold text-uppercase mb-0">Inventory Monitoring HO</h1>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-success btn-sm" id="export-excel-btn">
                <i class="mdi mdi-file-excel me-1"></i> Export Excel
            </button>
            <button type="button" class="btn btn-danger btn-sm" id="export-pdf-btn">
                <i class="mdi mdi-file-pdf me-1"></i> Export PDF
            </button>
        </div>
    </div>
    <div class="d-flex flex-wrap align-items-end gap-3">
        <div class="w-auto">
            <select class="form-control btn btn-primary" id="site_id" name="site_id" >
                @foreach($sites as $site)
                <option value="{{ $site->site_id }}" {{ $firstSite->site_id == $site->site_id ? 'selected' : '' }}>
                    {{ $site->site_name }}
                </option>
                @endforeach
            </select>
        </div>
        <div class="w-auto">
            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}" >
        </div>

        <div class="w-auto">
            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
        </div>

        <div class="w-auto">
            <input type="text" class="form-control" id="search" name="search" oninput="loadData()" placeholder="Cari Nama/Kode Barang">
        </div>
    </div>
    <div class="table-responsive mt-3">
        <table class="table table-striped table-hover">
            <thead class="table-dark text-white">
                <tr>
                    <th class="p-2">Nama Barang </th>
                    <th class="p-2">Min Stock</th>
                    <th class="p-2">Max Stock</th>
                    <th class="p-2">Stok Saat Ini</th>
                    <th class="p-2">Jumlah Masuk (In)</th>
                    <th class="p-2">Jumlah Keluar (Out)</th>
                    <th class="p-2">Status</th>
                    <th class="p-2">Persentasi</th>
                    <th class="p-2">Prioritas</th>
                    <th class="p-2">Prioritas Pada</th>
                </tr>
            </thead>
            <tbody id="inventory-table-body">

            </tbody>
        </table>
    </div>
</div>

<!-- Modal Detail -->
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" style="max-width: 98%; margin: 10px auto;">
        <div class="modal-content p-4">
            <div class="modal-header pb-0">
                <h5 class="h3 font-bold text-center text-uppercase" id="detailModalLabel">Detail Pergerakan Barang</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h4 class="font-bold h6">Track In</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="bg-primary text-white">
                                    <tr>
                                        <th class="p-2">Tanggal Masuk</th>
                                        <th class="p-2">Jumlah</th>
                                        <th class="p-2">Nama Admin</th>
                                        <th class="p-2">Catatan</th>
                                    </tr>
                                </thead>
                                <tbody id="in-table-body">
                                    <!-- Data IN akan dimuat di sini -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h4 class="font-bold h6">Track Out</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="bg-success text-white">
                                    <tr>
                                        <th class="p-2">Tanggal keluar</th>
                                        <th class="p-2">Jumlah</th>
                                        <th class="p-2">Nama Admin</th>
                                        <th class="p-2">Notes</th>
                                    </tr>
                                </thead>
                                <tbody id="out-table-body">
                                    <!-- Data OUT akan dimuat di sini -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
